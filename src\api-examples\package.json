{"name": "recyclecharity-api", "version": "1.0.0", "description": "Backend API for RecycleCharity profile management with MongoDB", "main": "profile-api.js", "scripts": {"start": "node profile-api.js", "dev": "nodemon profile-api.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.6.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["nodejs", "express", "mongodb", "jwt", "profile", "api"], "author": "RecycleCharity Team", "license": "MIT"}
import apiService from './api.js';

class ProfileManager {
  constructor() {
    this.currentUser = null;
    this.init();
  }

  async init() {
    // <PERSON><PERSON>m tra authentication
    if (!apiService.isAuthenticated()) {
      window.location.href = './index.html';
      return;
    }

    this.setupEventListeners();
    await this.loadUserData();
  }

  setupEventListeners() {
    // Profile form
    const profileForm = document.getElementById('profileForm');
    if (profileForm) {
      profileForm.addEventListener('submit', this.handleUpdateProfile.bind(this));
    }

    // Reset profile button
    const resetProfileBtn = document.getElementById('resetProfileBtn');
    if (resetProfileBtn) {
      resetProfileBtn.addEventListener('click', this.resetProfileForm.bind(this));
    }

    // Change password form
    const changePasswordForm = document.getElementById('changePasswordForm');
    if (changePasswordForm) {
      changePasswordForm.addEventListener('submit', this.handleChangePassword.bind(this));
    }
  }

  async loadUserData() {
    try {
      const response = await apiService.getMe();
      this.currentUser = response.data.user;
      this.populateProfileForm();
      this.populateAccountSettings();
    } catch (error) {
      console.error('Error loading user data:', error);
      this.showError('profileFormFeedback', 'Không thể tải thông tin người dùng');
    }
  }

  populateProfileForm() {
    if (!this.currentUser) return;

    document.getElementById('profileName').value = this.currentUser.name || '';
    document.getElementById('profileEmail').value = this.currentUser.email || '';
    document.getElementById('profilePhone').value = this.currentUser.phone || '';
    document.getElementById('profileAddress').value = this.currentUser.address || '';
    document.getElementById('profileRole').value =
      this.currentUser.role === 'admin' ? 'Quản trị viên' : 'Thành viên';
  }

  populateAccountSettings() {
    if (!this.currentUser) return;

    document.getElementById('accountId').textContent = this.currentUser._id;
    document.getElementById('accountCreated').textContent =
      new Date(this.currentUser.createdAt).toLocaleDateString('vi-VN');
    document.getElementById('accountLastLogin').textContent =
      this.currentUser.lastLogin ?
      new Date(this.currentUser.lastLogin).toLocaleDateString('vi-VN') : 'Chưa có';

    const statusElement = document.getElementById('accountStatus');
    if (this.currentUser.isActive) {
      statusElement.textContent = 'Hoạt động';
      statusElement.className = 'badge bg-success';
    } else {
      statusElement.textContent = 'Không hoạt động';
      statusElement.className = 'badge bg-danger';
    }
  }

  async handleUpdateProfile(e) {
    e.preventDefault();

    const name = document.getElementById('profileName').value;
    const phone = document.getElementById('profilePhone').value;
    const address = document.getElementById('profileAddress').value;
    const feedback = document.getElementById('profileFormFeedback');

    try {
      this.showLoading(feedback, 'Đang cập nhật thông tin...');

      const response = await apiService.updateProfile({
        name,
        phone,
        address
      });

      this.currentUser = response.data.user;
      this.showSuccess(feedback, response.message);

    } catch (error) {
      this.showError(feedback, error.message);
    }
  }

  resetProfileForm() {
    this.populateProfileForm();
    document.getElementById('profileFormFeedback').innerHTML = '';
  }

  async handleChangePassword(e) {
    e.preventDefault();

    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmNewPassword = document.getElementById('confirmNewPassword').value;
    const feedback = document.getElementById('changePasswordFormFeedback');

    // Validate password confirmation
    if (newPassword !== confirmNewPassword) {
      this.showError(feedback, 'Mật khẩu mới và xác nhận mật khẩu không khớp');
      return;
    }

    // Validate password strength
    if (!this.validatePassword(newPassword)) {
      this.showError(feedback, 'Mật khẩu mới phải có ít nhất 6 ký tự, bao gồm chữ hoa, chữ thường và số');
      return;
    }

    try {
      this.showLoading(feedback, 'Đang đổi mật khẩu...');

      const response = await apiService.changePassword({
        currentPassword,
        newPassword,
        confirmPassword: confirmNewPassword
      });

      this.showSuccess(feedback, response.message);

      // Reset form
      document.getElementById('changePasswordForm').reset();

    } catch (error) {
      this.showError(feedback, error.message);
    }
  }

  validatePassword(password) {
    // At least 6 characters, contains uppercase, lowercase, and number
    const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{6,}$/;
    return regex.test(password);
  }



  // Helper methods
  showLoading(element, message) {
    element.innerHTML = `
      <div class="alert alert-info d-flex align-items-center" role="alert">
        <div class="spinner-border spinner-border-sm me-2" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        ${message}
      </div>
    `;
  }

  showSuccess(element, message) {
    element.innerHTML = `
      <div class="alert alert-success" role="alert">
        <i class="bi bi-check-circle me-2"></i>${message}
      </div>
    `;
  }

  showError(element, message) {
    element.innerHTML = `
      <div class="alert alert-danger" role="alert">
        <i class="bi bi-exclamation-triangle me-2"></i>${message}
      </div>
    `;
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new ProfileManager();
});

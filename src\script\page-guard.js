import apiService from './api.js';

// Page Guard - <PERSON><PERSON><PERSON> vệ các trang yêu cầu authentication
class PageGuard {
  constructor() {
    this.init();
  }

  init() {
    this.checkAuthentication();
  }

  checkAuthentication() {
    const currentPage = this.getCurrentPage();
    const isAuthenticated = apiService.isAuthenticated();
    const user = apiService.getCurrentUser();

    // Kiểm tra các trang yêu cầu đăng nhập
    if (this.isProtectedPage(currentPage)) {
      if (!isAuthenticated) {
        this.redirectToHome('Vui lòng đăng nhập để truy cập trang này');
        return;
      }

      // Kiểm tra quyền admin cho trang admin
      if (currentPage === 'admin' && user?.role !== 'admin') {
        this.redirectToHome('Bạn không có quyền truy cập trang này');
        return;
      }
    }

    // Nếu đã đăng nhập và đang ở trang chủ, có thể hiển thị thông báo chào mừng
    if (isAuthenticated && currentPage === 'index') {
      this.showWelcomeMessage(user);
    }
  }

  getCurrentPage() {
    const path = window.location.pathname;
    const filename = path.split('/').pop();
    
    if (!filename || filename === '' || filename === 'index.html') {
      return 'index';
    }
    
    return filename.replace('.html', '');
  }

  isProtectedPage(page) {
    const protectedPages = ['profile', 'admin'];
    return protectedPages.includes(page);
  }

  redirectToHome(message) {
    // Lưu thông báo vào sessionStorage để hiển thị sau khi redirect
    if (message) {
      sessionStorage.setItem('redirectMessage', message);
    }
    
    // Redirect về trang chủ
    window.location.href = 'index.html';
  }

  showWelcomeMessage(user) {
    // Kiểm tra xem có thông báo redirect không
    const redirectMessage = sessionStorage.getItem('redirectMessage');
    if (redirectMessage) {
      this.showAlert(redirectMessage, 'warning');
      sessionStorage.removeItem('redirectMessage');
      return;
    }

    // Hiển thị thông báo chào mừng nếu vừa đăng nhập
    const justLoggedIn = sessionStorage.getItem('justLoggedIn');
    if (justLoggedIn) {
      this.showAlert(`Chào mừng ${user.name}! Bạn đã đăng nhập thành công.`, 'success');
      sessionStorage.removeItem('justLoggedIn');
    }
  }

  showAlert(message, type = 'info') {
    // Tạo alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 9999; max-width: 400px;';
    
    alertDiv.innerHTML = `
      <i class="bi bi-${this.getAlertIcon(type)} me-2"></i>
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Thêm vào body
    document.body.appendChild(alertDiv);

    // Tự động ẩn sau 5 giây
    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.remove();
      }
    }, 5000);
  }

  getAlertIcon(type) {
    const icons = {
      'success': 'check-circle',
      'warning': 'exclamation-triangle',
      'danger': 'x-circle',
      'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new PageGuard();
});

export default PageGuard;

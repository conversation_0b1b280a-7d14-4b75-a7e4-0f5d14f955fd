<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="../assets/icon/recycle.svg" />
    <title>Đ<PERSON>i quà - Recycle Charity</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />

    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/simplelightbox@2/dist/simple-lightbox.min.css"
    />
    <!-- Bootstrap Icons -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"
    />
    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Google Fonts-->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700;800;900&display=swap"
      rel="stylesheet"
    />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../stylesheets/main.scss" />
  </head>
  <body class="profile-page">
    <!-- Top Navigation -->
    <nav class="profile-navbar">
      <a href="./index.html" class="navbar-brand">
        <img src="../assets/icon/recycle.svg" alt="Recycle Charity" width="32" height="32">
        Recycle Charity
      </a>

      <div class="navbar-user">
        <div class="user-avatar">
          <i class="bi bi-person"></i>
        </div>
        <div class="user-info">
          <div class="user-name">Nguyễn Văn A</div>
          <div class="user-points">
            <i class="fas fa-coins"></i>1,250 xu
          </div>
        </div>
        <button class="navbar-toggle" onclick="toggleSidebar()">
          <i class="bi bi-list"></i>
        </button>
      </div>
    </nav>

    <!-- Sidebar -->
    <div class="profile-sidebar" id="profileSidebar">
      <div class="list-group list-group-flush">
        <a href="./profile.html" class="list-group-item list-group-item-action">
          <i class="bi bi-person"></i><span>Thông tin cá nhân</span>
        </a>
        <a href="./profile-rewards.html" class="list-group-item list-group-item-action active">
          <i class="fas fa-gift"></i><span>Đổi quà</span>
        </a>
        <a href="./profile-password.html" class="list-group-item list-group-item-action">
          <i class="bi bi-key"></i><span>Đổi mật khẩu</span>
        </a>
        <a href="./profile-settings.html" class="list-group-item list-group-item-action">
          <i class="bi bi-gear"></i><span>Cài đặt tài khoản</span>
        </a>
      </div>
    </div>

    <!-- main content -->
    <main class="profile-content" id="profileContent">
      <!-- Page Header -->
      <div class="profile-header text-center">
        <h1 class="display-5 fw-bold">
          <i class="fas fa-gift me-3"></i>Đổi quà tích xu
        </h1>
        <p class="lead">Sử dụng xu tích lũy để đổi những món quà ý nghĩa</p>
      </div>
              <!-- User Points Card -->
              <div class="card mb-4">
                <div class="card-body">
                  <div class="d-flex align-items-center">
                    <i class="fas fa-wallet me-3 fs-2 text-primary"></i>
                    <div>
                      <h6 class="mb-1 text-muted">Xu của bạn</h6>
                      <span class="fs-3 fw-bold text-primary" id="userPoints">1,250</span>
                      <span class="text-muted fs-5">xu</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Category Filters Card -->
              <div class="card mb-4">
                <div class="card-header">
                  <h6 class="card-title mb-0">
                    <i class="fas fa-tags me-2"></i>Danh mục quà tặng
                  </h6>
                </div>
                <div class="card-body">
                  <div class="btn-group flex-wrap" role="group">
                    <button type="button" class="btn btn-outline-primary active" data-category="all">
                      <i class="fas fa-th-large me-1"></i>Tất cả
                    </button>
                    <button type="button" class="btn btn-outline-primary" data-category="handmade">
                      <i class="fas fa-hand-holding-heart me-1"></i>Đồ thủ công
                    </button>
                    <button type="button" class="btn btn-outline-primary" data-category="toys">
                      <i class="fas fa-teddy-bear me-1"></i>Đồ chơi
                    </button>
                    <button type="button" class="btn btn-outline-primary" data-category="accessories">
                      <i class="fas fa-key me-1"></i>Phụ kiện
                    </button>
                  </div>
                </div>
              </div>

              <!-- Rewards Grid Card -->
              <div class="card">
                <div class="card-header">
                  <h6 class="card-title mb-0">
                    <i class="fas fa-gift me-2"></i>Danh sách quà tặng
                  </h6>
                </div>
                <div class="card-body">
                  <div class="row g-3" id="rewardsGrid">
                    <!-- Reward Item 1 -->
                    <div class="col-lg-4 col-md-6">
                      <div class="card h-100 reward-card">
                        <div class="position-relative">
                          <img src="../assets/images/rewards/teddy-bear.webp" class="card-img-top" alt="Gấu bông handmade" style="height: 200px; object-fit: cover;">
                          <span class="badge bg-success position-absolute top-0 end-0 m-2">Handmade</span>
                          <div class="position-absolute bottom-0 start-0 m-2">
                            <span class="badge bg-warning text-dark">
                              <i class="fas fa-coins me-1"></i>500 xu
                            </span>
                          </div>
                        </div>
                        <div class="card-body">
                          <h6 class="card-title">Gấu bông handmade</h6>
                          <p class="card-text small text-muted">Gấu bông được các em nhỏ làm thủ công từ vải tái chế.</p>
                          <div class="d-flex justify-content-between align-items-center">
                            <small class="text-success">Còn 15 món</small>
                            <button class="btn btn-primary btn-sm">
                              <i class="fas fa-exchange-alt me-1"></i>Đổi
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Reward Item 2 -->
                    <div class="col-lg-4 col-md-6">
                      <div class="card h-100 reward-card">
                        <div class="position-relative">
                          <img src="../assets/images/rewards/keychain.webp" class="card-img-top" alt="Móc khóa tái chế" style="height: 200px; object-fit: cover;">
                          <span class="badge bg-info position-absolute top-0 end-0 m-2">Eco-friendly</span>
                          <div class="position-absolute bottom-0 start-0 m-2">
                            <span class="badge bg-warning text-dark">
                              <i class="fas fa-coins me-1"></i>200 xu
                            </span>
                          </div>
                        </div>
                        <div class="card-body">
                          <h6 class="card-title">Móc khóa tái chế</h6>
                          <p class="card-text small text-muted">Móc khóa được làm từ nhựa tái chế, thiết kế sáng tạo.</p>
                          <div class="d-flex justify-content-between align-items-center">
                            <small class="text-success">Còn 25 món</small>
                            <button class="btn btn-primary btn-sm">
                              <i class="fas fa-exchange-alt me-1"></i>Đổi
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Reward Item 3 -->
                    <div class="col-lg-4 col-md-6">
                      <div class="card h-100 reward-card">
                        <div class="position-relative">
                          <img src="../assets/images/rewards/painting.webp" class="card-img-top" alt="Tranh vẽ tay" style="height: 200px; object-fit: cover;">
                          <span class="badge bg-danger position-absolute top-0 end-0 m-2">Limited</span>
                          <div class="position-absolute bottom-0 start-0 m-2">
                            <span class="badge bg-warning text-dark">
                              <i class="fas fa-coins me-1"></i>800 xu
                            </span>
                          </div>
                        </div>
                        <div class="card-body">
                          <h6 class="card-title">Tranh vẽ tay</h6>
                          <p class="card-text small text-muted">Bức tranh được vẽ bởi các em nhỏ, thể hiện tình yêu môi trường.</p>
                          <div class="d-flex justify-content-between align-items-center">
                            <small class="text-warning">Còn 8 món</small>
                            <button class="btn btn-primary btn-sm">
                              <i class="fas fa-exchange-alt me-1"></i>Đổi
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Reward Item 4 -->
                    <div class="col-lg-4 col-md-6">
                      <div class="card h-100 reward-card">
                        <div class="position-relative">
                          <img src="../assets/images/rewards/canvas-bag.webp" class="card-img-top" alt="Túi vải canvas" style="height: 200px; object-fit: cover;">
                          <span class="badge bg-info position-absolute top-0 end-0 m-2">Eco-friendly</span>
                          <div class="position-absolute bottom-0 start-0 m-2">
                            <span class="badge bg-warning text-dark">
                              <i class="fas fa-coins me-1"></i>350 xu
                            </span>
                          </div>
                        </div>
                        <div class="card-body">
                          <h6 class="card-title">Túi vải canvas</h6>
                          <p class="card-text small text-muted">Túi vải được thiết kế và in hình bởi các em.</p>
                          <div class="d-flex justify-content-between align-items-center">
                            <small class="text-success">Còn 20 món</small>
                            <button class="btn btn-primary btn-sm">
                              <i class="fas fa-exchange-alt me-1"></i>Đổi
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Reward Item 5 -->
                    <div class="col-lg-4 col-md-6">
                      <div class="card h-100 reward-card">
                        <div class="position-relative">
                          <img src="../assets/images/rewards/wool-doll.webp" class="card-img-top" alt="Búp bê len" style="height: 200px; object-fit: cover;">
                          <span class="badge bg-success position-absolute top-0 end-0 m-2">Handmade</span>
                          <div class="position-absolute bottom-0 start-0 m-2">
                            <span class="badge bg-warning text-dark">
                              <i class="fas fa-coins me-1"></i>600 xu
                            </span>
                          </div>
                        </div>
                        <div class="card-body">
                          <h6 class="card-title">Búp bê len</h6>
                          <p class="card-text small text-muted">Búp bê được đan len thủ công bởi các em nhỏ.</p>
                          <div class="d-flex justify-content-between align-items-center">
                            <small class="text-success">Còn 12 món</small>
                            <button class="btn btn-primary btn-sm">
                              <i class="fas fa-exchange-alt me-1"></i>Đổi
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Reward Item 6 -->
                    <div class="col-lg-4 col-md-6">
                      <div class="card h-100 reward-card">
                        <div class="position-relative">
                          <img src="../assets/images/rewards/bookmark.webp" class="card-img-top" alt="Bookmark handmade" style="height: 200px; object-fit: cover;">
                          <span class="badge bg-info position-absolute top-0 end-0 m-2">Eco-friendly</span>
                          <div class="position-absolute bottom-0 start-0 m-2">
                            <span class="badge bg-warning text-dark">
                              <i class="fas fa-coins me-1"></i>150 xu
                            </span>
                          </div>
                        </div>
                        <div class="card-body">
                          <h6 class="card-title">Bookmark handmade</h6>
                          <p class="card-text small text-muted">Bookmark được làm từ giấy tái chế, trang trí đẹp mắt.</p>
                          <div class="d-flex justify-content-between align-items-center">
                            <small class="text-success">Còn 30 món</small>
                            <button class="btn btn-primary btn-sm">
                              <i class="fas fa-exchange-alt me-1"></i>Đổi
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Reward Item 7 -->
                    <div class="col-lg-4 col-md-6">
                      <div class="card h-100 reward-card">
                        <div class="position-relative">
                          <img src="../assets/images/rewards/pencil-box.webp" class="card-img-top" alt="Hộp bút handmade" style="height: 200px; object-fit: cover;">
                          <span class="badge bg-success position-absolute top-0 end-0 m-2">Handmade</span>
                          <div class="position-absolute bottom-0 start-0 m-2">
                            <span class="badge bg-warning text-dark">
                              <i class="fas fa-coins me-1"></i>400 xu
                            </span>
                          </div>
                        </div>
                        <div class="card-body">
                          <h6 class="card-title">Hộp bút handmade</h6>
                          <p class="card-text small text-muted">Hộp bút được làm từ carton tái chế, trang trí sáng tạo.</p>
                          <div class="d-flex justify-content-between align-items-center">
                            <small class="text-success">Còn 18 món</small>
                            <button class="btn btn-primary btn-sm">
                              <i class="fas fa-exchange-alt me-1"></i>Đổi
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Reward Item 8 -->
                    <div class="col-lg-4 col-md-6">
                      <div class="card h-100 reward-card">
                        <div class="position-relative">
                          <img src="../assets/images/rewards/wooden-toy-car.webp" class="card-img-top" alt="Xe đồ chơi gỗ" style="height: 200px; object-fit: cover;">
                          <span class="badge bg-info position-absolute top-0 end-0 m-2">Eco-friendly</span>
                          <div class="position-absolute bottom-0 start-0 m-2">
                            <span class="badge bg-warning text-dark">
                              <i class="fas fa-coins me-1"></i>700 xu
                            </span>
                          </div>
                        </div>
                        <div class="card-body">
                          <h6 class="card-title">Xe đồ chơi gỗ</h6>
                          <p class="card-text small text-muted">Xe đồ chơi được làm từ gỗ tái chế, an toàn và thân thiện.</p>
                          <div class="d-flex justify-content-between align-items-center">
                            <small class="text-success">Còn 10 món</small>
                            <button class="btn btn-primary btn-sm">
                              <i class="fas fa-exchange-alt me-1"></i>Đổi
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Bootstrap JS Bundle -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
      crossorigin="anonymous"
    ></script>

    <!-- Custom JS -->
    <script type="module" src="../script/auth.js"></script>
    <script type="module" src="../script/rewards.js"></script>

    <script>
      function toggleSidebar() {
        const sidebar = document.getElementById('profileSidebar');
        const content = document.getElementById('profileContent');

        sidebar.classList.toggle('collapsed');
        content.classList.toggle('expanded');
      }

      // Mobile sidebar toggle
      function toggleMobileSidebar() {
        const sidebar = document.getElementById('profileSidebar');
        sidebar.classList.toggle('show');
      }

      // Close sidebar when clicking outside on mobile
      document.addEventListener('click', function(event) {
        const sidebar = document.getElementById('profileSidebar');
        const toggle = document.querySelector('.navbar-toggle');

        if (window.innerWidth <= 768 &&
            !sidebar.contains(event.target) &&
            !toggle.contains(event.target)) {
          sidebar.classList.remove('show');
        }
      });

      // Handle window resize
      window.addEventListener('resize', function() {
        const sidebar = document.getElementById('profileSidebar');
        const content = document.getElementById('profileContent');

        if (window.innerWidth <= 768) {
          sidebar.classList.remove('collapsed');
          content.classList.remove('expanded');
        }
      });
    </script>
  </body>
</html>

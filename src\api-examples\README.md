# RecycleCharity Profile API with MongoDB

Backend API để quản lý profile người dùng với MongoDB integration.

## 🚀 Setup Instructions

### 1. Install Dependencies
```bash
cd src/api-examples
npm install
```

### 2. Setup MongoDB
- Install MongoDB locally hoặc sử dụng MongoDB Atlas
- Tạo database tên `recyclecharity`
- MongoDB sẽ tự động tạo collections khi có data

### 3. Environment Variables
Tạo file `.env`:
```env
PORT=3000
MONGODB_URI=mongodb://localhost:27017/recyclecharity
JWT_SECRET=your-super-secret-jwt-key-here
NODE_ENV=development
```

### 4. Start Server
```bash
# Development mode với auto-reload
npm run dev

# Production mode
npm start
```

Server sẽ chạy tại: `http://localhost:3000`

## 📡 API Endpoints

### Authentication
- `POST /api/auth/register` - Đ<PERSON>ng ký user mới
- `POST /api/auth/login` - <PERSON>ăng nhập
- `GET /api/auth/me` - <PERSON><PERSON><PERSON> thông tin user hiện tại

### Profile Management
- `GET /api/user/profile` - Lấy profile đầy đủ
- `PUT /api/user/profile` - Cập nhật profile

## 🔧 Frontend Integration

### 1. Update API URL
Trong file `src/script/profile-common.js`, cập nhật:
```javascript
const API_BASE_URL = 'http://localhost:3000/api';
```

### 2. Authentication Token
Frontend cần lưu JWT token sau khi login:
```javascript
// Sau khi login thành công
localStorage.setItem('authToken', response.token);
```

### 3. Test với Demo Data
1. Chạy backend server
2. Mở frontend profile page
3. Click "Demo User" button
4. Data sẽ được lưu vào MongoDB

## 📊 Database Schema

### User Collection
```javascript
{
  _id: ObjectId,
  fullName: String,
  username: String (unique),
  email: String (unique),
  password: String (hashed),
  phone: String,
  address: String,
  role: String,
  gender: String,
  points: Number,
  accountId: String (unique),
  loginCount: Number,
  lastLogin: Date,
  lastActivity: Date,
  createdAt: Date,
  updatedAt: Date
}
```

## 🔒 Security Features

- JWT authentication
- Password hashing với bcrypt
- Input validation
- CORS protection
- Error handling

## 🧪 Testing

### Manual Testing
1. Register new user:
```bash
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "fullName": "Nguyễn Văn Test",
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

2. Login:
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

3. Get profile (với token từ login):
```bash
curl -X GET http://localhost:3000/api/user/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🚨 Troubleshooting

### Common Issues

1. **MongoDB Connection Error**
   - Kiểm tra MongoDB đã chạy: `mongod`
   - Kiểm tra connection string trong `.env`

2. **CORS Error**
   - Đảm bảo frontend chạy trên port khác backend
   - Kiểm tra CORS config trong `profile-api.js`

3. **JWT Token Error**
   - Kiểm tra token được lưu đúng trong localStorage
   - Kiểm tra JWT_SECRET trong `.env`

4. **Port Already in Use**
   - Thay đổi PORT trong `.env`
   - Hoặc kill process: `lsof -ti:3000 | xargs kill`

## 📈 Next Steps

1. **Add Validation**
   - Sử dụng joi hoặc express-validator
   - Validate email format, password strength

2. **Add Logging**
   - Winston hoặc morgan cho logging
   - Error tracking với Sentry

3. **Add Rate Limiting**
   - express-rate-limit
   - Prevent brute force attacks

4. **Add Tests**
   - Jest cho unit tests
   - Supertest cho API tests

5. **Production Deployment**
   - Docker containerization
   - Environment-specific configs
   - Database migrations

/* Modern Profile Page Design */

// Color palette from variables
$primary: #1e40af;
$primary-light: #3b82f6;
$secondary: #64748b;
$accent: #f59e0b;
$success: #059669;
$danger: #dc2626;
$warning: #f59e0b;
$info: #0ea5e9;

$bg-main: #f8fafc;
$bg-card: #ffffff;
$text-dark: #1e293b;
$text-muted: #64748b;
$text-light: #ffffff;
$border-color: #e2e8f0;

.profile-page {
  background: $bg-main;
  min-height: 100vh;
  padding-top: 80px;
  font-family: 'Nunito', sans-serif;
  color: $text-dark;

  // Top Navigation Bar
  .profile-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    z-index: 1000;
    display: flex;
    align-items: center;
    padding: 0 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

    .navbar-brand {
      display: flex;
      align-items: center;
      color: $text-light;
      text-decoration: none;
      font-weight: 700;
      font-size: 1.25rem;
      margin-right: 2rem;

      img {
        margin-right: 0.75rem;
      }

      &:hover {
        color: rgba($text-light, 0.9);
      }
    }

    .navbar-user {
      margin-left: auto;
      display: flex;
      align-items: center;
      color: $text-light;

      .user-avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: rgba($text-light, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.75rem;
        font-size: 1.1rem;
      }

      .user-info {
        display: flex;
        flex-direction: column;
        margin-right: 1rem;

        .user-name {
          font-weight: 600;
          font-size: 0.9rem;
          line-height: 1.2;
        }

        .user-points {
          font-size: 0.8rem;
          opacity: 0.9;
          display: flex;
          align-items: center;

          i {
            margin-right: 0.25rem;
            color: $accent;
          }
        }
      }

      .navbar-toggle {
        background: none;
        border: none;
        color: $text-light;
        font-size: 1.2rem;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.5rem;
        transition: background-color 0.3s ease;

        &:hover {
          background: rgba($text-light, 0.1);
        }
      }
    }
  }

  .profile-header {
    background: linear-gradient(135deg, $primary 0%, $primary-light 100%);
    color: $text-light;
    border-radius: 1.5rem;
    padding: 3rem 2rem;
    margin-bottom: 3rem;
    text-align: center;
    box-shadow: 0 20px 40px rgba($primary, 0.15);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="60" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
      opacity: 0.3;
    }

    h1 {
      font-size: 2.75rem;
      font-weight: 800;
      margin-bottom: 0.75rem;
      position: relative;
      z-index: 1;
    }

    .lead {
      font-size: 1.2rem;
      opacity: 0.9;
      margin-bottom: 0;
      position: relative;
      z-index: 1;
    }
  }

  .profile-sidebar {
    position: fixed;
    left: 0;
    top: 60px;
    bottom: 0;
    width: 280px;
    background: $bg-card;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    z-index: 999;
    overflow-y: auto;

    &.collapsed {
      transform: translateX(-220px);
      width: 60px;

      .list-group-item {
        span {
          opacity: 0;
          visibility: hidden;
        }

        i {
          margin-right: 0;
        }
      }
    }

    .list-group-item {
      border: none;
      padding: 1.25rem 1.5rem;
      display: flex;
      align-items: center;
      transition: all 0.3s ease;
      cursor: pointer;
      font-weight: 500;
      color: $text-dark;
      background: transparent;
      border-radius: 0;

      &:hover {
        background: rgba($primary, 0.08);
        color: $primary;

        i {
          transform: scale(1.1);
        }
      }

      &.active {
        background: linear-gradient(135deg, $primary 0%, $primary-light 100%);
        color: $text-light;
        font-weight: 600;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4px;
          background: $accent;
        }
      }

      i {
        margin-right: 1rem;
        font-size: 1.25rem;
        transition: all 0.3s ease;
        width: 20px;
        text-align: center;
        flex-shrink: 0;
      }

      span {
        font-size: 0.95rem;
        transition: all 0.3s ease;
        white-space: nowrap;
      }
    }
  }

  .profile-content {
    margin-left: 280px;
    padding: 2rem;
    transition: margin-left 0.3s ease;

    &.expanded {
      margin-left: 60px;
    }
    .card {
      border: none;
      border-radius: 1.5rem;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
      margin-bottom: 2rem;
      background: $bg-card;
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
      }

      .card-header {
        background: linear-gradient(135deg, $primary 0%, $primary-light 100%);
        color: $text-light;
        border: none;
        padding: 2rem;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 1px;
          background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        }

        h5, h6 {
          margin: 0;
          font-weight: 700;
          display: flex;
          align-items: center;

          i {
            margin-right: 0.75rem;
            font-size: 1.25rem;
          }
        }
      }

      .card-body {
        padding: 2.5rem;
        background: $bg-card;
      }
    }

    form {
      .form-label {
        font-weight: 600;
        color: $text-dark;
        margin-bottom: 0.75rem;
        font-size: 0.95rem;
      }

      .form-control {
        border: 2px solid $border-color;
        border-radius: 1rem;
        padding: 1rem 1.25rem;
        transition: all 0.3s ease;
        font-weight: 500;
        background: $bg-card;

        &:focus {
          border-color: $primary;
          box-shadow: 0 0 0 0.25rem rgba($primary, 0.15);
          background: $bg-card;
        }

        &[readonly] {
          background: #f8fafc;
          border-color: #cbd5e1;
          color: $text-muted;
        }
      }

      .form-text {
        color: $text-muted;
        font-size: 0.875rem;
        margin-top: 0.5rem;
      }

      .btn {
        border-radius: 1rem;
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 0.95rem;
        transition: all 0.3s ease;

        &.btn-primary {
          background: linear-gradient(135deg, $primary 0%, $primary-light 100%);
          border: none;
          color: $text-light;

          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba($primary, 0.3);
          }
        }

        &.btn-outline-secondary {
          color: $text-muted;
          border: 2px solid $border-color;
          background: transparent;

          &:hover {
            background: $primary;
            border-color: $primary;
            color: $text-light;
            transform: translateY(-2px);
          }
        }

        &.btn-warning {
          background: linear-gradient(135deg, $warning 0%, #f97316 100%);
          border: none;
          color: $text-dark;

          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba($warning, 0.3);
          }
        }
      }
    }
  }

  .alert {
    border: none;
    border-radius: 1rem;
    padding: 1.25rem 1.75rem;
    font-weight: 500;
    margin-top: 1.5rem;

    &.alert-success {
      background: linear-gradient(135deg, $success 0%, #10b981 100%);
      color: $text-light;
      border-left: 4px solid #047857;
    }
    &.alert-danger  {
      background: linear-gradient(135deg, $danger 0%, #ef4444 100%);
      color: $text-light;
      border-left: 4px solid #b91c1c;
    }
    &.alert-info    {
      background: linear-gradient(135deg, $info 0%, #3b82f6 100%);
      color: $text-light;
      border-left: 4px solid #1d4ed8;
    }
    &.alert-warning {
      background: linear-gradient(135deg, $warning 0%, #f97316 100%);
      color: $text-dark;
      border-left: 4px solid #ea580c;
    }
  }

  // Rewards specific styles
  .reward-card {
    transition: all 0.3s ease;
    border: 1px solid $border-color;
    border-radius: 1.25rem;
    overflow: hidden;
    background: $bg-card;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 15px 40px rgba($primary, 0.15);
      border-color: $primary;
    }

    .card-img-top {
      transition: transform 0.3s ease;
    }

    &:hover .card-img-top {
      transform: scale(1.08);
    }

    .badge {
      font-weight: 600;
      padding: 0.5rem 0.75rem;
      border-radius: 0.75rem;
    }
  }

  // Loading states
  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;

    .loading-content {
      text-align: center;
      padding: 2rem;
      background: $bg-card;
      border-radius: 1rem;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

      .spinner-border {
        color: $primary;
        margin-bottom: 1rem;
      }

      p {
        margin: 0;
        color: $text-muted;
        font-weight: 500;
      }
    }
  }

  // Connection status indicator
  .connection-status {
    position: fixed;
    top: 80px;
    right: 20px;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 500;
    z-index: 1000;
    transition: all 0.3s ease;

    &.online {
      background: linear-gradient(135deg, $success 0%, #10b981 100%);
      color: white;
    }

    &.offline {
      background: linear-gradient(135deg, $danger 0%, #ef4444 100%);
      color: white;
    }

    &.syncing {
      background: linear-gradient(135deg, $warning 0%, #f97316 100%);
      color: $text-dark;
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    padding-top: 60px;

    .profile-navbar {
      .navbar-user .user-info {
        display: none;
      }
    }

    .profile-sidebar {
      transform: translateX(-100%);
      width: 280px;

      &.show {
        transform: translateX(0);
      }

      &.collapsed {
        transform: translateX(-100%);
      }
    }

    .profile-content {
      margin-left: 0;
      padding: 1rem;

      &.expanded {
        margin-left: 0;
      }

      .card {
        .card-header {
          padding: 1.5rem;
        }

        .card-body {
          padding: 1.5rem;
        }
      }
    }

    .btn {
      width: 100%;
      margin-bottom: 0.75rem;
    }
  }

  @media (max-width: 576px) {
    .profile-header {
      h1 {
        font-size: 1.75rem;
      }
    }

    .reward-card {
      &:hover {
        transform: translateY(-4px);
      }
    }
  }
}

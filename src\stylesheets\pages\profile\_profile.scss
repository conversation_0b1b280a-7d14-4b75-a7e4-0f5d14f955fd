// Profile Page Styles
.profile-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding-top: 100px;

  .profile-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

    h1 {
      font-weight: 700;
      margin-bottom: 0.5rem;
    }

    .lead {
      opacity: 0.9;
      font-size: 1.1rem;
    }
  }

  .profile-sidebar {
    .card {
      border: none;
      border-radius: 15px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
      overflow: hidden;

      .list-group-item {
        border: none;
        padding: 1rem 1.5rem;
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          background-color: #f8f9fa;
          transform: translateX(5px);
        }

        &.active {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          font-weight: 600;

          &:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transform: translateX(0);
          }
        }

        i {
          font-size: 1.1rem;
          width: 20px;
        }
      }
    }
  }

  .profile-content {
    .card {
      border: none;
      border-radius: 15px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
      overflow: hidden;

      .card-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border: none;
        padding: 1.5rem;

        h5 {
          margin: 0;
          font-weight: 600;
        }

        i {
          font-size: 1.2rem;
        }
      }

      .card-body {
        padding: 2rem;
      }
    }
  }

  .tab-content-item {
    display: none;

    &.active {
      display: block;
      animation: fadeInUp 0.5s ease;
    }
  }

  // Form Styles
  .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
  }

  .form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;

    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    &[readonly] {
      background-color: #f8f9fa;
      border-color: #dee2e6;
    }
  }

  textarea.form-control {
    resize: vertical;
    min-height: 100px;
  }

  // Button Styles
  .btn {
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;

    &.btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
      }
    }

    &.btn-warning {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      border: none;
      color: white;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(240, 147, 251, 0.4);
      }
    }

    &.btn-outline-secondary {
      border: 2px solid #6c757d;
      color: #6c757d;

      &:hover {
        background-color: #6c757d;
        transform: translateY(-2px);
      }
    }
  }

  // Alert Styles
  .alert {
    border: none;
    border-radius: 10px;
    padding: 1rem 1.5rem;

    &.alert-success {
      background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
      color: white;
    }

    &.alert-danger {
      background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
      color: white;
    }

    &.alert-info {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
    }

    &.alert-warning {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      color: white;
    }
  }

  // Badge Styles
  .badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;

    &.bg-success {
      background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
    }

    &.bg-danger {
      background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%) !important;
    }
  }

  // Account Settings Styles
  .account-info {
    ul {
      li {
        padding: 0.5rem 0;
        border-bottom: 1px solid #e9ecef;

        &:last-child {
          border-bottom: none;
        }

        strong {
          color: #495057;
          min-width: 150px;
          display: inline-block;
        }
      }
    }
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .profile-page {
    padding-top: 80px;

    .profile-header {
      padding: 1.5rem;
      text-align: center;

      h1 {
        font-size: 1.8rem;
      }
    }

    .profile-sidebar {
      margin-bottom: 2rem;

      .list-group-item {
        text-align: center;
        padding: 1rem;

        span {
          display: block;
          margin-top: 0.5rem;
        }
      }
    }

    .profile-content {
      .card-body {
        padding: 1.5rem;
      }
    }

    .btn {
      width: 100%;
      margin-bottom: 0.5rem;
    }
  }
}

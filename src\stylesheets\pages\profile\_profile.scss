/* Enhanced Profile Page Styles */

// Color palette
$primary: #5A6FD2;
$secondary: #9E79EA;
$accent: #FFC658;
$bg-gradient: linear-gradient(135deg, #F0F4FD 0%, #D8E2F8 100%);
$text-dark: #2E3A59;
$text-light: #FFFFFF;

.profile-page {
  background: $bg-gradient;
  min-height: 100vh;
  padding-top: 100px;
  font-family: 'Nunito', sans-serif;
  color: $text-dark;

  .profile-header {
    background: linear-gradient(135deg, $primary 0%, $secondary 100%);
    color: $text-light;
    border-radius: 1.25rem;
    padding: 2rem;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

    h1 {
      font-size: 2.5rem;
      font-weight: 800;
      margin-bottom: 0.5rem;
    }

    .lead {
      font-size: 1.1rem;
      opacity: 0.85;
      margin-bottom: 0;
    }
  }

  .profile-sidebar {
    .card {
      border: none;
      border-radius: 1rem;
      overflow: hidden;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);

      .list-group-item {
        border: none;
        padding: 1rem 1.5rem;
        display: flex;
        align-items: center;
        border-radius: 0 1rem 1rem 0;
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          background: rgba($primary, 0.1);
          transform: translateX(5px);
        }

        &.active {
          background: linear-gradient(135deg, $primary 0%, $secondary 100%);
          color: $text-light;
          font-weight: 600;

          &:hover {
            transform: none;
          }
        }

        i {
          margin-right: 0.75rem;
          font-size: 1.2rem;
        }
      }
    }
  }

  .profile-content {
    .card {
      border: none;
      border-radius: 1rem;
      overflow: hidden;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
      margin-bottom: 2rem;

      .card-header {
        background: linear-gradient(135deg, #FF9A8B 0%, #FF6A88 100%);
        color: $text-light;
        border: none;
        padding: 1.5rem;

        h5 {
          margin: 0;
          font-weight: 600;
        }
      }

      .card-body {
        padding: 2rem;
        background: $text-light;
      }
    }

    form {
      .form-label {
        font-weight: 600;
        color: darken($text-dark, 20%);
        margin-bottom: 0.5rem;
      }

      .form-control {
        border: 2px solid #e0e6f0;
        border-radius: 0.75rem;
        padding: 0.75rem 1rem;
        transition: border-color 0.3s ease, box-shadow 0.3s ease;

        &:focus {
          border-color: $primary;
          box-shadow: 0 0 0 0.2rem rgba($primary, 0.25);
        }

        &[readonly] {
          background: #F8F9FA;
          border-color: #DEE2E6;
        }
      }

      .btn {
        border-radius: 0.75rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;

        &.btn-primary {
          background: linear-gradient(135deg, $primary 0%, $secondary 100%);
          border: none;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba($primary, 0.4);
          }
        }

        &.btn-outline-secondary {
          color: $text-dark;
          border: 2px solid $primary;

          &:hover {
            background: rgba($primary, 0.1);
            transform: translateY(-2px);
          }
        }
      }
    }
  }

  .alert {
    border: none;
    border-radius: 0.75rem;
    padding: 1rem 1.5rem;

    &.alert-success { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); color: $text-light; }
    &.alert-danger  { background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%); color: $text-light; }
    &.alert-info    { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: $text-light; }
    &.alert-warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: $text-light; }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    padding-top: 80px;

    .profile-header {
      padding: 1.5rem;

      h1 { font-size: 1.8rem; }
    }

    .profile-sidebar { margin-bottom: 2rem; }

    .btn { width: 100%; margin-bottom: 0.5rem; }
  }
}

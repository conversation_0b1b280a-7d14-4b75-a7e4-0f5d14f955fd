import apiService from './api.js';

// Authentication Manager
class AuthManager {
  constructor() {
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.updateUI();
  }

  setupEventListeners() {
    // Form đăng nhập
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
      loginForm.addEventListener('submit', this.handleLogin.bind(this));
    }

    // Form đăng ký
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
      registerForm.addEventListener('submit', this.handleRegister.bind(this));
    }

    // Modal auth tabs
    const authModal = document.getElementById('authModal');
    if (authModal) {
      authModal.addEventListener('show.bs.modal', this.handleModalShow.bind(this));
    }

    // Logout button (sẽ được thêm động)
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('logout-btn')) {
        this.handleLogout();
      }
    });
  }

  // Xử lý đăng nhập
  async handleLogin(e) {
    e.preventDefault();

    const email = document.getElementById('loginEmail').value.trim();
    const password = document.getElementById('loginPassword').value;
    const feedback = document.getElementById('loginFormFeedback');

    // Frontend validation
    if (!email) {
      this.showError(feedback, 'Vui lòng nhập email');
      return;
    }

    if (!this.isValidEmail(email)) {
      this.showError(feedback, 'Email không hợp lệ');
      return;
    }

    if (!password) {
      this.showError(feedback, 'Vui lòng nhập mật khẩu');
      return;
    }

    try {
      this.showLoading(feedback, 'Đang đăng nhập...');

      const response = await apiService.login({ email, password });

      this.showSuccess(feedback, response.message);

      // Đóng modal và cập nhật UI
      setTimeout(() => {
        const modal = bootstrap.Modal.getInstance(document.getElementById('authModal'));
        modal.hide();
        this.updateUI();
        this.resetForms();
      }, 1000);

    } catch (error) {
      this.showError(feedback, error.message);
    }
  }

  // Xử lý đăng ký
  async handleRegister(e) {
    e.preventDefault();

    const name = document.getElementById('registerFullName').value.trim();
    const email = document.getElementById('registerEmail').value.trim();
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('registerConfirmPassword').value;
    const feedback = document.getElementById('registerFormFeedback');

    // Frontend validation
    if (!name) {
      this.showError(feedback, 'Vui lòng nhập tên tài khoản');
      return;
    }

    if (name.length < 2 || name.length > 50) {
      this.showError(feedback, 'Tên tài khoản phải từ 2-50 ký tự');
      return;
    }

    if (!email) {
      this.showError(feedback, 'Vui lòng nhập email');
      return;
    }

    if (!this.isValidEmail(email)) {
      this.showError(feedback, 'Email không hợp lệ');
      return;
    }

    if (!password) {
      this.showError(feedback, 'Vui lòng nhập mật khẩu');
      return;
    }

    if (password.length < 6) {
      this.showError(feedback, 'Mật khẩu phải có ít nhất 6 ký tự');
      return;
    }

    // Validate password confirmation if field exists
    const confirmPasswordField = document.getElementById('registerConfirmPassword');
    if (confirmPasswordField && password !== confirmPassword) {
      this.showError(feedback, 'Mật khẩu xác nhận không khớp');
      return;
    }

    try {
      this.showLoading(feedback, 'Đang đăng ký...');

      const response = await apiService.register({
        name,
        email,
        password
      });

      this.showSuccess(feedback, response.message);

      // Đóng modal và cập nhật UI
      setTimeout(() => {
        const modal = bootstrap.Modal.getInstance(document.getElementById('authModal'));
        modal.hide();
        this.updateUI();
        this.resetForms();
      }, 1000);

    } catch (error) {
      this.showError(feedback, error.message);
    }
  }

  // Xử lý đăng xuất
  handleLogout() {
    if (confirm('Bạn có chắc chắn muốn đăng xuất?')) {
      apiService.logout();
    }
  }

  // Xử lý hiển thị modal
  handleModalShow(e) {
    const button = e.relatedTarget;
    const tab = button?.getAttribute('data-tab');

    if (tab === 'register') {
      document.getElementById('tab-register').click();
    } else {
      document.getElementById('tab-login').click();
    }
  }

  // Cập nhật UI dựa trên trạng thái đăng nhập
  updateUI() {
    const isAuthenticated = apiService.isAuthenticated();
    const user = apiService.getCurrentUser();

    // Tìm các elements
    const authButtons = document.getElementById('authButtons');
    const userDropdown = document.getElementById('userDropdown');
    const userName = document.getElementById('userName');
    const userRole = document.getElementById('userRole');
    const adminMenuItem = document.getElementById('adminMenuItem');

    if (!authButtons || !userDropdown) return;

    if (isAuthenticated && user) {
      // Ẩn auth buttons, hiển thị user dropdown
      authButtons.classList.add('d-none');
      userDropdown.classList.remove('d-none');

      // Cập nhật thông tin user
      if (userName) userName.textContent = user.name;
      if (userRole) userRole.textContent = user.role === 'admin' ? 'Quản trị viên' : 'Thành viên';

      // Hiển thị menu admin nếu user là admin
      if (adminMenuItem) {
        if (user.role === 'admin') {
          adminMenuItem.classList.remove('d-none');
        } else {
          adminMenuItem.classList.add('d-none');
        }
      }
    } else {
      // Hiển thị auth buttons, ẩn user dropdown
      authButtons.classList.remove('d-none');
      userDropdown.classList.add('d-none');
    }
  }

  // Reset forms
  resetForms() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');

    if (loginForm) loginForm.reset();
    if (registerForm) registerForm.reset();

    // Clear feedback messages
    const feedbacks = document.querySelectorAll('[id$="FormFeedback"]');
    feedbacks.forEach(feedback => feedback.innerHTML = '');
  }

  // Validation helper methods
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Helper methods để hiển thị messages
  showLoading(element, message) {
    element.innerHTML = `
      <div class="alert alert-info d-flex align-items-center" role="alert">
        <div class="spinner-border spinner-border-sm me-2" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        ${message}
      </div>
    `;
  }

  showSuccess(element, message) {
    element.innerHTML = `
      <div class="alert alert-success" role="alert">
        <i class="bi bi-check-circle me-2"></i>${message}
      </div>
    `;
  }

  showError(element, message) {
    element.innerHTML = `
      <div class="alert alert-danger" role="alert">
        <i class="bi bi-exclamation-triangle me-2"></i>${message}
      </div>
    `;
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new AuthManager();
});

export default AuthManager;

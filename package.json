{"name": "recyclecharity", "private": true, "version": "0.0.0", "scripts": {"dev": "parcel ./src/pages/*.html --port 3000", "dev:profile": "parcel ./src/pages/profile.html --port 3001", "dev:admin": "parcel ./src/pages/admin.html --port 3002", "build": "npm run clean && npm run build:all", "build:all": "parcel build ./src/pages/*.html --dist-dir dist --public-url ./", "build:main": "parcel build ./src/pages/index.html ./src/pages/about.html ./src/pages/collection.html ./src/pages/contact.html ./src/pages/education.html ./src/pages/support.html --dist-dir dist --public-url ./", "build:auth": "parcel build ./src/pages/profile.html ./src/pages/admin.html --dist-dir dist --public-url ./", "clean": "rimraf dist .parcel-cache", "preview": "parcel serve dist --port 8080", "start": "npm run dev"}, "devDependencies": {"@parcel/transformer-sass": "^2.14.4", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "events": "^3.3.0", "os-browserify": "^0.3.0", "parcel": "^2.14.4", "path-browserify": "^1.0.1", "posthtml": "^0.16.6", "posthtml-doctype": "^1.1.1", "posthtml-include": "^2.0.1", "process": "^0.11.10", "rimraf": "^5.0.5", "sass": "^1.88.0", "stream-browserify": "^3.0.0", "string_decoder": "^1.3.0", "vm-browserify": "^1.1.2"}, "dependencies": {"aos": "^2.3.4", "axios": "^1.9.0", "dotenv": "^16.5.0", "leaflet": "^1.9.4", "node-fetch": "^3.3.2", "sweetalert2": "^11.21.2"}}
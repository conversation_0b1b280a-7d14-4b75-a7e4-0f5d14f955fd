# 🚀 Build & Development Guide - Recycle Charity

## 📋 Available Scripts

### Development Scripts
```bash
# Chạy tất cả trang trong development mode (port 3000)
npm run dev

# Chạy riêng trang profile (port 3001)
npm run dev:profile

# Chạy riêng trang admin (port 3002)  
npm run dev:admin

# Alias cho npm run dev
npm start
```

### Build Scripts
```bash
# Build tất cả trang
npm run build

# Build chỉ các trang chính (không bao gồm profile, admin)
npm run build:main

# Build chỉ trang profile và admin
npm run build:auth

# Xóa cache và dist folder
npm run clean
```

### Preview Scripts
```bash
# Preview trang đã build (port 8080)
npm run preview
```

## 🎯 Workflow Recommended

### 1. Development
```bash
# Cài đặt dependencies
npm install

# Chạy development server
npm run dev

# Mở browser: http://localhost:3000
```

### 2. Test Authentication Pages
```bash
# Test trang profile riêng
npm run dev:profile
# Mở: http://localhost:3001

# Test trang admin riêng  
npm run dev:admin
# Mở: http://localhost:3002
```

### 3. Build for Production
```bash
# Build tất cả
npm run build

# Hoặc build từng phần
npm run build:main    # Trang chính
npm run build:auth    # Profile + Admin

# Preview kết quả
npm run preview
```

## 📁 Structure After Build

```
dist/
├── index.html          # Trang chủ
├── about.html          # Giới thiệu  
├── collection.html     # Thu gom
├── contact.html        # Liên hệ
├── education.html      # Giáo dục
├── support.html        # Hỗ trợ AI
├── profile.html        # Thông tin cá nhân (no header/footer)
├── admin.html          # Quản lý (no header/footer)
└── assets/            # CSS, JS, Images
```

## 🔧 Configuration Files

### `.parcelrc`
- Tắt tối ưu hóa hình ảnh và JS để tránh lỗi build
- Sử dụng reporter CLI đơn giản

### `package.json`
- Scripts được tổ chức theo chức năng
- Sử dụng rimraf để clean cross-platform
- Port riêng biệt cho từng trang

## 🚨 Troubleshooting

### Lỗi Build
```bash
# Xóa cache và thử lại
npm run clean
npm run build
```

### Lỗi Port đã sử dụng
```bash
# Kill process trên port
npx kill-port 3000
# Hoặc sử dụng port khác
parcel ./src/pages/*.html --port 3001
```

### Lỗi Authentication
- Đảm bảo backend đang chạy trên port 5001
- Kiểm tra CORS settings
- Xem console browser để debug

## 📝 Notes

1. **Profile & Admin pages**: Không có header/footer, thiết kế độc lập
2. **Authentication**: Dropdown menu có sẵn trong HTML, không tạo động
3. **Build**: Tất cả trang sẽ được build với đường dẫn tương đối
4. **Development**: Hot reload hoạt động cho tất cả trang

## 🎨 Design Features

- **Gradient Headers**: Tím-xanh đẹp mắt
- **Responsive**: Bootstrap 5.3.2
- **Icons**: Bootstrap Icons
- **Animations**: Hover effects, transitions
- **Forms**: Rounded corners, focus states

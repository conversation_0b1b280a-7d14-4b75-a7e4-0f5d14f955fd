# 🚀 Build & Development Guide - Recycle Charity

## 📋 Scripts đã sửa và hoạt động tốt

### Development
```bash
# Chạy development server cho tất cả trang
npm run dev
# Mở: http://localhost:1234
```

### Build
```bash
# Build 3 trang chính: index, profile, admin
npm run build
# Sử dụng --no-scope-hoist để tránh lỗi build
```

### Preview
```bash
# Preview trang đã build tại http://127.0.0.1:8080
npm run preview
# Sử dụng http-server thay vì parcel serve
```

## 🎯 Workflow hoàn chỉnh

### 1. Development
```bash
# Cài đặt dependencies
npm install

# Chạy development server
npm run dev

# Mở browser: http://localhost:1234
# Tất cả trang sẽ được serve tự động:
# - http://localhost:1234/index.html (trang chủ)
# - http://localhost:1234/profile.html (profile - no header/footer)
# - http://localhost:1234/admin.html (admin - no header/footer)
# - http://localhost:1234/about.html
# - http://localhost:1234/collection.html
# - http://localhost:1234/contact.html
# - http://localhost:1234/education.html
# - http://localhost:1234/support.html
```

### 2. Build for Production
```bash
# Build 3 trang: index, profile, admin
npm run build

# Preview kết quả tại http://127.0.0.1:8080
npm run preview
```

## 📁 Structure After Build

```
dist/
├── index.html          # Trang chủ (có header/footer)
├── profile.html        # Thông tin cá nhân (no header/footer)
├── admin.html          # Quản lý (no header/footer)
├── about.html          # Giới thiệu
├── collection.html     # Thu gom
├── contact.html        # Liên hệ
├── education.html      # Giáo dục
├── support.html        # Hỗ trợ AI
└── assets/            # CSS, JS, Images (với hash)
```

## ✅ Các vấn đề đã sửa:

1. **npm run dev**: ✅ Chạy tất cả trang HTML, server tại localhost:1234
2. **npm run build**: ✅ Build 3 trang chính với --no-scope-hoist
3. **npm run preview**: ✅ Sử dụng http-server thay vì parcel serve
4. **Đường dẫn include**: ✅ Sửa thành `layout/` với root config
5. **Profile & Admin**: ✅ Thiết kế đẹp, bỏ header/footer

## 🚨 Troubleshooting

### Lỗi Build
```bash
# Xóa cache và thử lại
rm -rf .parcel-cache dist
npm run build
```

### Lỗi Development
```bash
# Kill process và chạy lại
# Ctrl+C để dừng
npm run dev
```

## 🎨 Design Features

- **Gradient Headers**: Tím-xanh đẹp mắt cho profile/admin
- **Responsive**: Bootstrap 5.3.2
- **Icons**: Bootstrap Icons
- **Animations**: Hover effects, transitions
- **Forms**: Rounded corners, focus states

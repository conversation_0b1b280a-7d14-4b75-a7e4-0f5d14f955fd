const { body } = require('express-validator');

// Validation cho đăng ký
const validateRegister = [
  body('name')
    .trim()
    .notEmpty()
    .withMessage('Tên tài khoản không được để trống')
    .isLength({ min: 2, max: 50 })
    .withMessage('Tên tài khoản phải từ 2-50 ký tự'),

  body('email')
    .isEmail()
    .withMessage('Email không hợp lệ')
    .normalizeEmail(),

  body('password')
    .isLength({ min: 6 })
    .withMessage('Mật khẩu phải có ít nhất 6 ký tự'),

  body('phone')
    .optional()
    .matches(/^[0-9]{10,11}$/)
    .withMessage('Số điện thoại không hợp lệ'),

  body('address')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Đ<PERSON><PERSON> chỉ không được vượt quá 200 ký tự')
];

// Validation cho đăng nhập
const validateLogin = [
  body('email')
    .isEmail()
    .withMessage('Email không hợp lệ')
    .normalizeEmail(),

  body('password')
    .notEmpty()
    .withMessage('Mật khẩu không được để trống')
];

// Validation cho cập nhật thông tin cá nhân
const validateUpdateProfile = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Tên tài khoản phải từ 2-50 ký tự'),

  body('phone')
    .optional()
    .matches(/^[0-9]{10,11}$/)
    .withMessage('Số điện thoại không hợp lệ'),

  body('address')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Địa chỉ không được vượt quá 200 ký tự')
];

// Validation cho đổi mật khẩu
const validateChangePassword = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Mật khẩu hiện tại không được để trống'),

  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('Mật khẩu mới phải có ít nhất 6 ký tự'),

  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('Xác nhận mật khẩu không khớp');
      }
      return true;
    })
];

// Validation cho tạo/cập nhật user (Admin)
const validateUserManagement = [
  body('name')
    .trim()
    .notEmpty()
    .withMessage('Tên tài khoản không được để trống')
    .isLength({ min: 2, max: 50 })
    .withMessage('Tên tài khoản phải từ 2-50 ký tự'),

  body('email')
    .isEmail()
    .withMessage('Email không hợp lệ')
    .normalizeEmail(),

  body('phone')
    .optional()
    .matches(/^[0-9]{10,11}$/)
    .withMessage('Số điện thoại không hợp lệ'),

  body('address')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Địa chỉ không được vượt quá 200 ký tự'),

  body('role')
    .optional()
    .isIn(['user', 'admin'])
    .withMessage('Vai trò phải là user hoặc admin'),

  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('Trạng thái active phải là true hoặc false')
];

// Validation cho tạo user mới (Admin) - bao gồm password
const validateCreateUser = [
  ...validateUserManagement,
  body('password')
    .isLength({ min: 6 })
    .withMessage('Mật khẩu phải có ít nhất 6 ký tự')
];

module.exports = {
  validateRegister,
  validateLogin,
  validateUpdateProfile,
  validateChangePassword,
  validateUserManagement,
  validateCreateUser
};

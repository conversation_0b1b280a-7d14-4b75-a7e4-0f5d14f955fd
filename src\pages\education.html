<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="../assets/icon/recycle.svg" />

    <title>Recycle Charity</title>
    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />
    <!-- Bootstrap Icons -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"
    />
    <!-- Google Fonts-->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700;800;900&display=swap"
      rel="stylesheet"
    />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../stylesheets/main.scss" />
  </head>
  <body>
    <!-- header -->
    <include src="./layout/header.html" locals="{}"> </include>

    <!-- main -->
    <include src="./layout/main-education.html" locals="{}"> </include>

    <!-- footer -->
    <include src="./layout/footer.html" locals="{}"> </include>

    <!-- Bootstrap JS Bundle -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
      crossorigin="anonymous"
    ></script>
    <script type="module" src="../script/article.js"></script>
    <script type="module" src="../script/question.js"></script>
    <script type="module" src="../script/main.js"></script>
    <!-- Custom JS -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const yearEl = document.getElementById("currentYear");
        if (yearEl) {
          yearEl.textContent = new Date().getFullYear();
        }
      });
    </script>
  </body>
</html>

/**
 * Profile Common Functions
 * Shared functionality across all profile pages
 */

// Default profile data
const defaultProfileData = {
  fullName: '',
  username: '',
  email: '',
  phone: '',
  address: '',
  role: 'Thành viên',
  gender: '',
  points: 0,
  accountId: 'RC001',
  accountCreated: '2024-01-15',
  lastLogin: '2024-12-20 14:30',
  loginCount: 45,
  lastActivity: '2024-12-20 15:45'
};

// Profile data management
class ProfileManager {
  constructor() {
    this.data = { ...defaultProfileData };
    this.loadFromStorage();
  }

  // Load data from localStorage
  loadFromStorage() {
    const stored = localStorage.getItem('profileData');
    if (stored) {
      try {
        const parsedData = JSON.parse(stored);
        this.data = { ...defaultProfileData, ...parsedData };
      } catch (error) {
        console.error('Error parsing stored profile data:', error);
        this.data = { ...defaultProfileData };
      }
    } else {
      // Try to get initial data from auth or other sources
      this.loadInitialData();
    }
  }

  // Load initial data from various sources
  loadInitialData() {
    // Try to get data from auth system
    const authUser = this.getAuthenticatedUser();
    if (authUser) {
      this.data = { ...defaultProfileData, ...authUser };
      this.saveToStorage();
    }
  }

  // Get authenticated user info (placeholder - integrate with your auth system)
  getAuthenticatedUser() {
    // This should integrate with your actual authentication system
    // For demo purposes, you can uncomment and modify this:

    // Check if user is logged in via your auth system
    const authData = localStorage.getItem('authUser');
    if (authData) {
      try {
        const user = JSON.parse(authData);
        return {
          fullName: user.name || user.fullName || '',
          username: user.username || '',
          email: user.email || '',
          phone: user.phone || '',
          address: user.address || '',
          points: user.points || 0,
          role: user.role || 'Thành viên',
          gender: user.gender || ''
        };
      } catch (error) {
        console.error('Error parsing auth user data:', error);
      }
    }

    return null;
  }

  // Demo function to set sample user data
  setDemoUser() {
    const demoUser = {
      fullName: 'Trần Thị Mai',
      username: 'tranthimai',
      email: '<EMAIL>',
      phone: '0987654321',
      address: 'Quận 1, TP.HCM',
      role: 'Thành viên',
      gender: 'female',
      points: 2500
    };

    this.updateData(demoUser);
  }

  // Save data to localStorage
  saveToStorage() {
    try {
      localStorage.setItem('profileData', JSON.stringify(this.data));
    } catch (error) {
      console.error('Error saving profile data:', error);
    }
  }

  // Get profile data
  getData() {
    return { ...this.data };
  }

  // Update profile data
  updateData(newData) {
    this.data = { ...this.data, ...newData };
    this.saveToStorage();
    this.updateAllHeaders();
  }

  // Update header information across all pages
  updateAllHeaders() {
    const headerUserName = document.getElementById('headerUserName');
    const headerUserPoints = document.getElementById('headerUserPoints');

    if (headerUserName) {
      // Priority: fullName > username > email prefix > default
      let displayName = 'Người dùng';

      if (this.data.fullName && this.data.fullName.trim()) {
        displayName = this.data.fullName.trim();
      } else if (this.data.username && this.data.username.trim()) {
        displayName = this.data.username.trim();
      } else if (this.data.email && this.data.email.trim()) {
        // Extract name from email (before @)
        const emailPrefix = this.data.email.split('@')[0];
        displayName = emailPrefix || 'Người dùng';
      }

      headerUserName.textContent = displayName;
    }

    if (headerUserPoints) {
      headerUserPoints.textContent = this.data.points.toLocaleString();
    }

    // Update user points display in rewards page
    const userPointsDisplay = document.getElementById('userPoints');
    if (userPointsDisplay) {
      userPointsDisplay.textContent = this.data.points.toLocaleString();
    }
  }

  // Load profile data into form (for profile.html)
  loadIntoForm() {
    const elements = {
      'profileFullName': this.data.fullName,
      'profileName': this.data.username,
      'profileEmail': this.data.email,
      'profilePhone': this.data.phone,
      'profileAddress': this.data.address,
      'profileRole': this.data.role,
      'profileGender': this.data.gender
    };

    Object.entries(elements).forEach(([id, value]) => {
      const element = document.getElementById(id);
      if (element) {
        element.value = value || '';
      }
    });
  }

  // Update from form (for profile.html)
  updateFromForm() {
    const formData = {
      fullName: document.getElementById('profileFullName')?.value || '',
      username: document.getElementById('profileName')?.value || '',
      phone: document.getElementById('profilePhone')?.value || '',
      address: document.getElementById('profileAddress')?.value || '',
      gender: document.getElementById('profileGender')?.value || ''
    };

    this.updateData(formData);
  }

  // Load account info into settings page
  loadAccountInfo() {
    const elements = {
      'accountId': this.data.accountId,
      'accountCreated': this.formatDate(this.data.accountCreated),
      'accountLastLogin': this.data.lastLogin,
      'loginCount': this.data.loginCount,
      'lastActivity': this.data.lastActivity
    };

    Object.entries(elements).forEach(([id, value]) => {
      const element = document.getElementById(id);
      if (element) {
        element.textContent = value || '-';
      }
    });
  }

  // Format date for display
  formatDate(dateString) {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('vi-VN');
    } catch (error) {
      return dateString;
    }
  }

  // Spend points (for rewards)
  spendPoints(amount) {
    if (this.data.points >= amount) {
      this.data.points -= amount;
      this.saveToStorage();
      this.updateAllHeaders();
      return true;
    }
    return false;
  }

  // Add points
  addPoints(amount) {
    this.data.points += amount;
    this.saveToStorage();
    this.updateAllHeaders();
  }
}

// Global profile manager instance
window.profileManager = new ProfileManager();

// Sidebar functions
function toggleSidebar() {
  const sidebar = document.getElementById('profileSidebar');
  const content = document.getElementById('profileContent');

  sidebar.classList.toggle('collapsed');
  content.classList.toggle('expanded');
}

// Mobile sidebar toggle
function toggleMobileSidebar() {
  const sidebar = document.getElementById('profileSidebar');
  sidebar.classList.toggle('show');
}

// Close sidebar when clicking outside on mobile
document.addEventListener('click', function(event) {
  const sidebar = document.getElementById('profileSidebar');
  const toggle = document.querySelector('.navbar-toggle');

  if (window.innerWidth <= 768 &&
      !sidebar.contains(event.target) &&
      !toggle.contains(event.target)) {
    sidebar.classList.remove('show');
  }
});

// Handle window resize
window.addEventListener('resize', function() {
  const sidebar = document.getElementById('profileSidebar');
  const content = document.getElementById('profileContent');

  if (window.innerWidth <= 768) {
    sidebar.classList.remove('collapsed');
    content.classList.remove('expanded');
  }
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
  // Update header information
  window.profileManager.updateAllHeaders();

  // Page-specific initialization
  const currentPage = window.location.pathname.split('/').pop();

  switch (currentPage) {
    case 'profile.html':
      initProfilePage();
      break;
    case 'profile-rewards.html':
      initRewardsPage();
      break;
    case 'profile-settings.html':
      initSettingsPage();
      break;
    case 'profile-password.html':
      initPasswordPage();
      break;
  }
});

// Page-specific initialization functions
function initProfilePage() {
  window.profileManager.loadIntoForm();

  // Form submission
  const profileForm = document.getElementById('profileForm');
  if (profileForm) {
    profileForm.addEventListener('submit', function(e) {
      e.preventDefault();
      window.profileManager.updateFromForm();

      // Show success message
      const feedback = document.getElementById('profileFormFeedback');
      if (feedback) {
        feedback.innerHTML = '<div class="alert alert-success"><i class="bi bi-check-circle me-2"></i>Thông tin đã được cập nhật thành công!</div>';

        setTimeout(() => {
          feedback.innerHTML = '';
        }, 3000);
      }
    });
  }

  // Reset form
  const resetBtn = document.getElementById('resetProfileBtn');
  if (resetBtn) {
    resetBtn.addEventListener('click', function() {
      window.profileManager.loadIntoForm();
    });
  }

  // Demo user button
  const demoBtn = document.getElementById('demoUserBtn');
  if (demoBtn) {
    demoBtn.addEventListener('click', function() {
      window.profileManager.setDemoUser();
      window.profileManager.loadIntoForm();

      // Show info message
      const feedback = document.getElementById('profileFormFeedback');
      if (feedback) {
        feedback.innerHTML = '<div class="alert alert-info"><i class="bi bi-info-circle me-2"></i>Đã tải thông tin demo user. Bạn có thể chỉnh sửa và lưu lại.</div>';

        setTimeout(() => {
          feedback.innerHTML = '';
        }, 3000);
      }
    });
  }
}

function initRewardsPage() {
  // Additional rewards page initialization if needed
}

function initSettingsPage() {
  window.profileManager.loadAccountInfo();
}

function initPasswordPage() {
  // Password page initialization if needed
}

// Export for use in other scripts
window.toggleSidebar = toggleSidebar;
window.toggleMobileSidebar = toggleMobileSidebar;

/**
 * Profile Common Functions
 * Shared functionality across all profile pages with MongoDB integration
 */

// API Configuration
const API_BASE_URL = 'http://localhost:3000/api'; // Adjust to your backend URL
const API_ENDPOINTS = {
  profile: '/user/profile',
  updateProfile: '/user/profile',
  auth: '/auth/me'
};

// Default profile data
const defaultProfileData = {
  _id: null,
  fullName: '',
  username: '',
  email: '',
  phone: '',
  address: '',
  role: 'Thành viên',
  gender: '',
  points: 0,
  accountId: '',
  accountCreated: null,
  lastLogin: null,
  loginCount: 0,
  lastActivity: null
};

// Profile data management with MongoDB integration
class ProfileManager {
  constructor() {
    this.data = { ...defaultProfileData };
    this.isLoading = false;
    this.init();
  }

  // Initialize profile manager
  async init() {
    try {
      await this.loadFromMongoDB();
    } catch (error) {
      console.error('Failed to load from MongoDB, falling back to localStorage:', error);
      this.loadFromStorage();
    }
  }

  // Load data from MongoDB via API
  async loadFromMongoDB() {
    this.isLoading = true;

    try {
      // Show connection status
      if (window.connectionManager) {
        window.connectionManager.setSyncing(true);
      }

      const token = this.getAuthToken();
      if (!token) {
        throw new Error('Không tìm thấy token xác thực. Vui lòng đăng nhập lại.');
      }

      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.profile}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.');
        } else if (response.status === 404) {
          throw new Error('Không tìm thấy thông tin người dùng.');
        } else {
          throw new Error(`Lỗi server: ${response.status}`);
        }
      }

      const userData = await response.json();
      this.data = { ...defaultProfileData, ...userData };

      // Cache in localStorage as backup
      this.saveToStorage();
      this.updateAllHeaders();

    } catch (error) {
      console.error('Error loading profile from MongoDB:', error);
      throw error;
    } finally {
      this.isLoading = false;

      // Hide connection status
      if (window.connectionManager) {
        window.connectionManager.setSyncing(false);
      }
    }
  }

  // Save data to MongoDB via API
  async saveToMongoDB() {
    this.isLoading = true;

    try {
      // Show connection status
      if (window.connectionManager) {
        window.connectionManager.setSyncing(true);
      }

      const token = this.getAuthToken();
      if (!token) {
        throw new Error('Không tìm thấy token xác thực. Vui lòng đăng nhập lại.');
      }

      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.updateProfile}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(this.data)
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.');
        } else if (response.status === 403) {
          throw new Error('Bạn không có quyền cập nhật thông tin này.');
        } else if (response.status === 422) {
          const errorData = await response.json();
          throw new Error(`Dữ liệu không hợp lệ: ${errorData.message || 'Vui lòng kiểm tra lại thông tin.'}`);
        } else {
          throw new Error(`Lỗi server: ${response.status}`);
        }
      }

      const updatedData = await response.json();
      this.data = { ...this.data, ...updatedData };

      // Update localStorage cache
      this.saveToStorage();
      this.updateAllHeaders();

      return updatedData;
    } catch (error) {
      console.error('Error saving profile to MongoDB:', error);
      throw error;
    } finally {
      this.isLoading = false;

      // Hide connection status
      if (window.connectionManager) {
        window.connectionManager.setSyncing(false);
      }
    }
  }

  // Get authentication token
  getAuthToken() {
    // Try multiple sources for auth token
    return localStorage.getItem('authToken') ||
           localStorage.getItem('token') ||
           sessionStorage.getItem('authToken') ||
           sessionStorage.getItem('token');
  }

  // Load data from localStorage (fallback)
  loadFromStorage() {
    const stored = localStorage.getItem('profileData');
    if (stored) {
      try {
        const parsedData = JSON.parse(stored);
        this.data = { ...defaultProfileData, ...parsedData };
        this.updateAllHeaders();
      } catch (error) {
        console.error('Error parsing stored profile data:', error);
        this.data = { ...defaultProfileData };
      }
    }
  }

  // Get authenticated user info (placeholder - integrate with your auth system)
  getAuthenticatedUser() {
    // This should integrate with your actual authentication system
    // For demo purposes, you can uncomment and modify this:

    // Check if user is logged in via your auth system
    const authData = localStorage.getItem('authUser');
    if (authData) {
      try {
        const user = JSON.parse(authData);
        return {
          fullName: user.name || user.fullName || '',
          username: user.username || '',
          email: user.email || '',
          phone: user.phone || '',
          address: user.address || '',
          points: user.points || 0,
          role: user.role || 'Thành viên',
          gender: user.gender || ''
        };
      } catch (error) {
        console.error('Error parsing auth user data:', error);
      }
    }

    return null;
  }

  // Demo function to set sample user data
  async setDemoUser() {
    const demoUser = {
      fullName: 'Trần Thị Mai',
      username: 'tranthimai',
      email: '<EMAIL>',
      phone: '0987654321',
      address: 'Quận 1, TP.HCM',
      role: 'Thành viên',
      gender: 'female',
      points: 2500
    };

    return await this.updateData(demoUser);
  }

  // Save data to localStorage (cache)
  saveToStorage() {
    try {
      localStorage.setItem('profileData', JSON.stringify(this.data));
    } catch (error) {
      console.error('Error saving profile data to localStorage:', error);
    }
  }

  // Get profile data
  getData() {
    return { ...this.data };
  }

  // Update profile data (saves to MongoDB)
  async updateData(newData) {
    const oldData = { ...this.data };
    this.data = { ...this.data, ...newData };

    try {
      await this.saveToMongoDB();
      return { success: true, data: this.data };
    } catch (error) {
      // Rollback on error
      this.data = oldData;
      this.updateAllHeaders();
      return { success: false, error: error.message };
    }
  }

  // Refresh data from MongoDB
  async refreshData() {
    try {
      await this.loadFromMongoDB();
      return { success: true, data: this.data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Update header information across all pages
  updateAllHeaders() {
    const headerUserName = document.getElementById('headerUserName');
    const headerUserPoints = document.getElementById('headerUserPoints');

    if (headerUserName) {
      // Priority: fullName > username > email prefix > default
      let displayName = 'Người dùng';

      if (this.data.fullName && this.data.fullName.trim()) {
        displayName = this.data.fullName.trim();
      } else if (this.data.username && this.data.username.trim()) {
        displayName = this.data.username.trim();
      } else if (this.data.email && this.data.email.trim()) {
        // Extract name from email (before @)
        const emailPrefix = this.data.email.split('@')[0];
        displayName = emailPrefix || 'Người dùng';
      }

      headerUserName.textContent = displayName;
    }

    if (headerUserPoints) {
      headerUserPoints.textContent = this.data.points.toLocaleString();
    }

    // Update user points display in rewards page
    const userPointsDisplay = document.getElementById('userPoints');
    if (userPointsDisplay) {
      userPointsDisplay.textContent = this.data.points.toLocaleString();
    }
  }

  // Load profile data into form (for profile.html)
  loadIntoForm() {
    const elements = {
      'profileFullName': this.data.fullName,
      'profileName': this.data.username,
      'profileEmail': this.data.email,
      'profilePhone': this.data.phone,
      'profileAddress': this.data.address,
      'profileRole': this.data.role,
      'profileGender': this.data.gender
    };

    Object.entries(elements).forEach(([id, value]) => {
      const element = document.getElementById(id);
      if (element) {
        element.value = value || '';
      }
    });
  }

  // Update from form (for profile.html) - async version
  async updateFromForm() {
    const formData = {
      fullName: document.getElementById('profileFullName')?.value || '',
      username: document.getElementById('profileName')?.value || '',
      phone: document.getElementById('profilePhone')?.value || '',
      address: document.getElementById('profileAddress')?.value || '',
      gender: document.getElementById('profileGender')?.value || ''
    };

    return await this.updateData(formData);
  }

  // Load account info into settings page
  loadAccountInfo() {
    const elements = {
      'accountId': this.data.accountId,
      'accountCreated': this.formatDate(this.data.accountCreated),
      'accountLastLogin': this.data.lastLogin,
      'loginCount': this.data.loginCount,
      'lastActivity': this.data.lastActivity
    };

    Object.entries(elements).forEach(([id, value]) => {
      const element = document.getElementById(id);
      if (element) {
        element.textContent = value || '-';
      }
    });
  }

  // Format date for display
  formatDate(dateString) {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('vi-VN');
    } catch (error) {
      return dateString;
    }
  }

  // Spend points (for rewards)
  spendPoints(amount) {
    if (this.data.points >= amount) {
      this.data.points -= amount;
      this.saveToStorage();
      this.updateAllHeaders();
      return true;
    }
    return false;
  }

  // Add points
  addPoints(amount) {
    this.data.points += amount;
    this.saveToStorage();
    this.updateAllHeaders();
  }
}

// Connection status manager
class ConnectionManager {
  constructor() {
    this.isOnline = navigator.onLine;
    this.statusElement = null;
    this.init();
  }

  init() {
    this.createStatusIndicator();
    this.bindEvents();
    this.updateStatus();
  }

  createStatusIndicator() {
    this.statusElement = document.createElement('div');
    this.statusElement.className = 'connection-status';
    this.statusElement.id = 'connectionStatus';
    document.body.appendChild(this.statusElement);
  }

  bindEvents() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.updateStatus();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.updateStatus();
    });
  }

  updateStatus(syncing = false) {
    if (!this.statusElement) return;

    if (syncing) {
      this.statusElement.className = 'connection-status syncing';
      this.statusElement.innerHTML = '<i class="bi bi-arrow-repeat me-1"></i>Đang đồng bộ...';
    } else if (this.isOnline) {
      this.statusElement.className = 'connection-status online';
      this.statusElement.innerHTML = '<i class="bi bi-wifi me-1"></i>Đã kết nối';
    } else {
      this.statusElement.className = 'connection-status offline';
      this.statusElement.innerHTML = '<i class="bi bi-wifi-off me-1"></i>Mất kết nối';
    }

    // Auto hide after 3 seconds if online
    if (this.isOnline && !syncing) {
      setTimeout(() => {
        if (this.statusElement) {
          this.statusElement.style.opacity = '0';
          setTimeout(() => {
            if (this.statusElement) {
              this.statusElement.style.display = 'none';
            }
          }, 300);
        }
      }, 3000);
    } else {
      this.statusElement.style.display = 'block';
      this.statusElement.style.opacity = '1';
    }
  }

  setSyncing(syncing) {
    this.updateStatus(syncing);
  }
}

// Loading overlay manager
class LoadingManager {
  constructor() {
    this.overlay = null;
  }

  show(message = 'Đang tải...') {
    if (this.overlay) return;

    this.overlay = document.createElement('div');
    this.overlay.className = 'loading-overlay';
    this.overlay.innerHTML = `
      <div class="loading-content">
        <div class="spinner-border" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p>${message}</p>
      </div>
    `;
    document.body.appendChild(this.overlay);
  }

  hide() {
    if (this.overlay) {
      this.overlay.remove();
      this.overlay = null;
    }
  }
}

// Global instances
window.profileManager = new ProfileManager();
window.connectionManager = new ConnectionManager();
window.loadingManager = new LoadingManager();

// Sidebar functions
function toggleSidebar() {
  const sidebar = document.getElementById('profileSidebar');
  const content = document.getElementById('profileContent');

  sidebar.classList.toggle('collapsed');
  content.classList.toggle('expanded');
}

// Mobile sidebar toggle
function toggleMobileSidebar() {
  const sidebar = document.getElementById('profileSidebar');
  sidebar.classList.toggle('show');
}

// Close sidebar when clicking outside on mobile
document.addEventListener('click', function(event) {
  const sidebar = document.getElementById('profileSidebar');
  const toggle = document.querySelector('.navbar-toggle');

  if (window.innerWidth <= 768 &&
      !sidebar.contains(event.target) &&
      !toggle.contains(event.target)) {
    sidebar.classList.remove('show');
  }
});

// Handle window resize
window.addEventListener('resize', function() {
  const sidebar = document.getElementById('profileSidebar');
  const content = document.getElementById('profileContent');

  if (window.innerWidth <= 768) {
    sidebar.classList.remove('collapsed');
    content.classList.remove('expanded');
  }
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
  // Update header information
  window.profileManager.updateAllHeaders();

  // Page-specific initialization
  const currentPage = window.location.pathname.split('/').pop();

  switch (currentPage) {
    case 'profile.html':
      initProfilePage();
      break;
    case 'profile-rewards.html':
      initRewardsPage();
      break;
    case 'profile-settings.html':
      initSettingsPage();
      break;
    case 'profile-password.html':
      initPasswordPage();
      break;
  }
});

// Page-specific initialization functions
function initProfilePage() {
  window.profileManager.loadIntoForm();

  // Form submission with loading state
  const profileForm = document.getElementById('profileForm');
  if (profileForm) {
    profileForm.addEventListener('submit', async function(e) {
      e.preventDefault();

      const submitBtn = profileForm.querySelector('button[type="submit"]');
      const feedback = document.getElementById('profileFormFeedback');

      // Show loading state
      const originalText = submitBtn.innerHTML;
      submitBtn.innerHTML = '<i class="spinner-border spinner-border-sm me-2"></i>Đang cập nhật...';
      submitBtn.disabled = true;

      try {
        const result = await window.profileManager.updateFromForm();

        if (result.success) {
          feedback.innerHTML = '<div class="alert alert-success"><i class="bi bi-check-circle me-2"></i>Thông tin đã được cập nhật thành công!</div>';
        } else {
          feedback.innerHTML = `<div class="alert alert-danger"><i class="bi bi-exclamation-triangle me-2"></i>Lỗi: ${result.error}</div>`;
        }
      } catch (error) {
        feedback.innerHTML = `<div class="alert alert-danger"><i class="bi bi-exclamation-triangle me-2"></i>Lỗi: ${error.message}</div>`;
      } finally {
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        // Clear message after 5 seconds
        setTimeout(() => {
          feedback.innerHTML = '';
        }, 5000);
      }
    });
  }

  // Reset form
  const resetBtn = document.getElementById('resetProfileBtn');
  if (resetBtn) {
    resetBtn.addEventListener('click', async function() {
      const result = await window.profileManager.refreshData();
      if (result.success) {
        window.profileManager.loadIntoForm();

        const feedback = document.getElementById('profileFormFeedback');
        if (feedback) {
          feedback.innerHTML = '<div class="alert alert-info"><i class="bi bi-arrow-clockwise me-2"></i>Đã khôi phục thông tin từ server.</div>';
          setTimeout(() => {
            feedback.innerHTML = '';
          }, 3000);
        }
      }
    });
  }

  // Demo user button
  const demoBtn = document.getElementById('demoUserBtn');
  if (demoBtn) {
    demoBtn.addEventListener('click', async function() {
      const originalText = demoBtn.innerHTML;
      demoBtn.innerHTML = '<i class="spinner-border spinner-border-sm me-2"></i>Loading...';
      demoBtn.disabled = true;

      try {
        const result = await window.profileManager.setDemoUser();
        window.profileManager.loadIntoForm();

        const feedback = document.getElementById('profileFormFeedback');
        if (feedback) {
          if (result.success) {
            feedback.innerHTML = '<div class="alert alert-info"><i class="bi bi-info-circle me-2"></i>Đã tải thông tin demo user và lưu vào database.</div>';
          } else {
            feedback.innerHTML = `<div class="alert alert-warning"><i class="bi bi-exclamation-triangle me-2"></i>Demo user loaded locally: ${result.error}</div>`;
          }

          setTimeout(() => {
            feedback.innerHTML = '';
          }, 3000);
        }
      } finally {
        demoBtn.innerHTML = originalText;
        demoBtn.disabled = false;
      }
    });
  }
}

function initRewardsPage() {
  // Additional rewards page initialization if needed
}

function initSettingsPage() {
  window.profileManager.loadAccountInfo();
}

function initPasswordPage() {
  // Password page initialization if needed
}

// Export for use in other scripts
window.toggleSidebar = toggleSidebar;
window.toggleMobileSidebar = toggleMobileSidebar;

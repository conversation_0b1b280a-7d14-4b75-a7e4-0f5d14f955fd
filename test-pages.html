<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pages - Recycle Charity</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="text-center mb-5">
                    <h1 class="display-4 fw-bold text-primary">
                        <i class="bi bi-recycle me-3"></i>Recycle Charity
                    </h1>
                    <p class="lead">Test các trang đã được cập nhật</p>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-body text-center">
                                <i class="bi bi-person-circle display-1 text-primary mb-3"></i>
                                <h5 class="card-title">Trang Profile</h5>
                                <p class="card-text">Trang thông tin cá nhân với thiết kế mới, bỏ header/footer, thêm trường họ tên và giới tính.</p>
                                <a href="src/pages/profile.html" class="btn btn-primary">
                                    <i class="bi bi-arrow-right me-2"></i>Xem Profile
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-body text-center">
                                <i class="bi bi-gear-fill display-1 text-success mb-3"></i>
                                <h5 class="card-title">Trang Admin</h5>
                                <p class="card-text">Trang quản lý hệ thống với thiết kế mới, bỏ header/footer, gradient đẹp mắt.</p>
                                <a href="src/pages/admin.html" class="btn btn-success">
                                    <i class="bi bi-arrow-right me-2"></i>Xem Admin
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-body text-center">
                                <i class="bi bi-house-fill display-1 text-info mb-3"></i>
                                <h5 class="card-title">Trang Chủ</h5>
                                <p class="card-text">Trang chủ với authentication UI được cập nhật, dropdown menu thay vì tạo động.</p>
                                <a href="src/pages/index.html" class="btn btn-info">
                                    <i class="bi bi-arrow-right me-2"></i>Xem Trang Chủ
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-body text-center">
                                <i class="bi bi-code-slash display-1 text-warning mb-3"></i>
                                <h5 class="card-title">Dev Server</h5>
                                <p class="card-text">Chạy development server để test các tính năng authentication và navigation.</p>
                                <div class="alert alert-warning mt-3">
                                    <small><strong>Lệnh:</strong> <code>npm run dev</code></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-5">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-list-check me-2"></i>Các thay đổi đã thực hiện</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">Profile Page:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Bỏ header và footer</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Thêm gradient header</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Thêm trường "Họ và tên"</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Thêm trường "Giới tính"</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Cập nhật CSS theo màu project</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">Admin Page:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Bỏ header và footer</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Thêm gradient header</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Cập nhật stats cards với gradient</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Cải thiện UI/UX</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Thêm hover effects</li>
                                </ul>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-info">Authentication:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Dropdown menu có sẵn trong HTML</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Show/hide thay vì tạo động</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Sửa đường dẫn cho build</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-warning">Build & Navigation:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Cập nhật package.json</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Page guard cho bảo vệ trang</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>Sửa đường dẫn tương đối</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

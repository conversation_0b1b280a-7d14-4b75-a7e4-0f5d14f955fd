<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="../assets/icon/recycle.svg" />
    <title>Đ<PERSON><PERSON> mật khẩu - Recycle Charity</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />

    <!-- Bootstrap Icons -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"
    />

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Google Fonts-->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700;800;900&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap"
      rel="stylesheet"
    />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../stylesheets/main.scss" />
    <style>
      .card {
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        border: 1px solid #e9ecef;
      }
      
      .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #dee2e6;
      }
    </style>
  </head>
  <body class="profile-page">
    <!-- header -->
    <include src="./layout/header.html" locals="{}"></include>

    <!-- main content -->
    <main class="container my-5">
      <div class="row justify-content-center">
        <div class="col-lg-10">
          <!-- Page Header -->
          <div class="profile-header text-center">
            <h1 class="display-5 fw-bold">
              <i class="bi bi-key me-3"></i>Đổi mật khẩu
            </h1>
            <p class="lead">Cập nhật mật khẩu để bảo mật tài khoản của bạn</p>
          </div>

          <!-- Profile Content -->
          <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-4 profile-sidebar">
              <div class="card">
                <div class="card-body">
                  <div class="list-group list-group-flush">
                    <a href="./profile.html" class="list-group-item list-group-item-action">
                      <i class="bi bi-person me-2"></i><span>Thông tin cá nhân</span>
                    </a>
                    <a href="./profile-rewards.html" class="list-group-item list-group-item-action">
                      <i class="fas fa-gift me-2"></i><span>Đổi quà</span>
                    </a>
                    <a href="./profile-password.html" class="list-group-item list-group-item-action active">
                      <i class="bi bi-key me-2"></i><span>Đổi mật khẩu</span>
                    </a>
                    <a href="./profile-settings.html" class="list-group-item list-group-item-action">
                      <i class="bi bi-gear me-2"></i><span>Cài đặt tài khoản</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 profile-content">
              <!-- Change Password -->
              <div class="card">
                <div class="card-header">
                  <h5 class="card-title mb-0">
                    <i class="bi bi-key me-2"></i>Đổi mật khẩu
                  </h5>
                </div>
                <div class="card-body">
                  <form id="changePasswordForm">
                    <div class="mb-3">
                      <label for="currentPassword" class="form-label">Mật khẩu hiện tại *</label>
                      <input type="password" class="form-control" id="currentPassword" required>
                    </div>
                    <div class="mb-3">
                      <label for="newPassword" class="form-label">Mật khẩu mới *</label>
                      <input type="password" class="form-control" id="newPassword" required>
                      <div class="form-text">Mật khẩu phải có ít nhất 6 ký tự, bao gồm chữ hoa, chữ thường và số</div>
                    </div>
                    <div class="mb-3">
                      <label for="confirmNewPassword" class="form-label">Xác nhận mật khẩu mới *</label>
                      <input type="password" class="form-control" id="confirmNewPassword" required>
                    </div>
                    <button type="submit" class="btn btn-warning">
                      <i class="bi bi-shield-lock me-2"></i>Đổi mật khẩu
                    </button>
                    <div id="changePasswordFormFeedback" class="mt-3"></div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- footer -->
    <include src="./layout/footer.html" locals="{}"></include>

    <!-- Bootstrap JS Bundle -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
      crossorigin="anonymous"
    ></script>

    <!-- Custom JS -->
    <script type="module" src="../script/auth.js"></script>
    <script type="module" src="../script/profile.js"></script>
  </body>
</html>

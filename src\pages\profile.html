<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="../assets/icon/recycle.svg" />
    <title>Thông tin cá nhân - Recycle Charity</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />

    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/simplelightbox@2/dist/simple-lightbox.min.css"
    />
    <!-- Bootstrap Icons -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"
    />
    <!-- Google Fonts-->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700;800;900&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap"
      rel="stylesheet"
    />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../stylesheets/main.scss" />
  </head>
  <body class="profile-page">
    <!-- Top Navigation -->
    <nav class="profile-navbar">
      <a href="./index.html" class="navbar-brand">
        <img src="../assets/icon/recycle.svg" alt="Recycle Charity" width="32" height="32">
        Recycle Charity
      </a>

      <div class="navbar-user">
        <div class="user-avatar">
          <i class="bi bi-person"></i>
        </div>
        <div class="user-info">
          <div class="user-name">Nguyễn Văn A</div>
          <div class="user-points">
            <i class="fas fa-coins"></i>1,250 xu
          </div>
        </div>
        <button class="navbar-toggle" onclick="toggleSidebar()">
          <i class="bi bi-list"></i>
        </button>
      </div>
    </nav>

    <!-- Sidebar -->
    <div class="profile-sidebar" id="profileSidebar">
      <div class="list-group list-group-flush">
        <a href="./profile.html" class="list-group-item list-group-item-action active">
          <i class="bi bi-person"></i><span>Thông tin cá nhân</span>
        </a>
        <a href="./profile-rewards.html" class="list-group-item list-group-item-action">
          <i class="fas fa-gift"></i><span>Đổi quà</span>
        </a>
        <a href="./profile-password.html" class="list-group-item list-group-item-action">
          <i class="bi bi-key"></i><span>Đổi mật khẩu</span>
        </a>
        <a href="./profile-settings.html" class="list-group-item list-group-item-action">
          <i class="bi bi-gear"></i><span>Cài đặt tài khoản</span>
        </a>
      </div>
    </div>

    <!-- main content -->
    <main class="profile-content" id="profileContent">
      <!-- Page Header -->
      <div class="profile-header text-center">
        <h1 class="display-5 fw-bold">
          <i class="bi bi-person-circle me-3"></i>Thông tin cá nhân
        </h1>
        <p class="lead">Quản lý thông tin tài khoản của bạn</p>
      </div>

      <!-- Profile Info -->
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="bi bi-person me-2"></i>Thông tin cá nhân
          </h5>
        </div>
        <div class="card-body">
          <form id="profileForm">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="profileFullName" class="form-label">Họ và tên *</label>
                <input type="text" class="form-control" id="profileFullName" required>
              </div>
              <div class="col-md-6 mb-3">
                <label for="profileName" class="form-label">Tên tài khoản *</label>
                <input type="text" class="form-control" id="profileName" required>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="profileEmail" class="form-label">Email *</label>
                <input type="email" class="form-control" id="profileEmail" readonly>
                <div class="form-text">Email không thể thay đổi</div>
              </div>
              <div class="col-md-6 mb-3">
                <label for="profileGender" class="form-label">Giới tính</label>
                <select class="form-control" id="profileGender">
                  <option value="">Chọn giới tính</option>
                  <option value="male">Nam</option>
                  <option value="female">Nữ</option>
                  <option value="other">Khác</option>
                </select>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="profilePhone" class="form-label">Số điện thoại</label>
                <input type="tel" class="form-control" id="profilePhone">
              </div>
              <div class="col-md-6 mb-3">
                <label for="profileRole" class="form-label">Vai trò</label>
                <input type="text" class="form-control" id="profileRole" readonly>
              </div>
            </div>
            <div class="mb-3">
              <label for="profileAddress" class="form-label">Địa chỉ</label>
              <textarea class="form-control" id="profileAddress" rows="3"></textarea>
            </div>
            <div class="d-flex justify-content-between">
              <button type="submit" class="btn btn-primary">
                <i class="bi bi-check-lg me-2"></i>Cập nhật thông tin
              </button>
              <button type="button" class="btn btn-outline-secondary" id="resetProfileBtn">
                <i class="bi bi-arrow-clockwise me-2"></i>Khôi phục
              </button>
            </div>
            <div id="profileFormFeedback" class="mt-3"></div>
          </form>
        </div>
      </div>
    </main>

    <!-- Bootstrap JS Bundle -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
      crossorigin="anonymous"
    ></script>

    <!-- Custom JS -->
    <script type="module" src="../script/auth.js"></script>
    <script type="module" src="../script/profile.js"></script>

    <script>
      function toggleSidebar() {
        const sidebar = document.getElementById('profileSidebar');
        const content = document.getElementById('profileContent');

        sidebar.classList.toggle('collapsed');
        content.classList.toggle('expanded');
      }

      // Mobile sidebar toggle
      function toggleMobileSidebar() {
        const sidebar = document.getElementById('profileSidebar');
        sidebar.classList.toggle('show');
      }

      // Close sidebar when clicking outside on mobile
      document.addEventListener('click', function(event) {
        const sidebar = document.getElementById('profileSidebar');
        const toggle = document.querySelector('.navbar-toggle');

        if (window.innerWidth <= 768 &&
            !sidebar.contains(event.target) &&
            !toggle.contains(event.target)) {
          sidebar.classList.remove('show');
        }
      });

      // Handle window resize
      window.addEventListener('resize', function() {
        const sidebar = document.getElementById('profileSidebar');
        const content = document.getElementById('profileContent');

        if (window.innerWidth <= 768) {
          sidebar.classList.remove('collapsed');
          content.classList.remove('expanded');
        }
      });
    </script>
  </body>
</html>

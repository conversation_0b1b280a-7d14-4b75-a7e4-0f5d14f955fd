<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="../assets/icon/recycle.svg" />
    <title>Thông tin cá nhân - Recycle Charity</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />

    <!-- Bootstrap Icons -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"
    />

    <!-- Google Fonts-->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700;800;900&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap"
      rel="stylesheet"
    />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../stylesheets/main.scss" />
    <style>
      .reward-card {
        transition: all 0.3s ease;
        border: 1px solid #dee2e6;
        border-radius: 12px;
        overflow: hidden;
      }

      .reward-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border-color: #0d6efd;
      }

      .reward-card .card-img-top {
        transition: transform 0.3s ease;
      }

      .reward-card:hover .card-img-top {
        transform: scale(1.05);
      }

      .btn-group .btn {
        margin: 2px;
        border-radius: 20px;
      }

      .points-badge {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 25px;
        border-radius: 15px;
        display: inline-flex;
        align-items: center;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
      }

      /* Tab content spacing */
      .tab-content-item .card {
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        border: 1px solid #e9ecef;
      }

      .tab-content-item .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #dee2e6;
      }

      .fade-in {
        animation: fadeIn 0.5s ease-in-out;
      }

      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
      }
    </style>
  </head>
  <body class="profile-page">
    <!-- header -->
    <include src="./layout/header.html" locals="{}"></include>

    <!-- main content -->
    <main class="container my-5">
      <div class="row justify-content-center">
        <div class="col-lg-10">
          <!-- Page Header -->
          <div class="profile-header text-center">
            <h1 class="display-5 fw-bold">
              <i class="bi bi-person-circle me-3"></i>Thông tin cá nhân
            </h1>
            <p class="lead">Quản lý thông tin tài khoản của bạn</p>
          </div>

          <!-- Profile Content -->
          <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-4 profile-sidebar">
              <div class="card">
                <div class="card-body">
                  <div class="list-group list-group-flush">
                    <a href="./profile.html" class="list-group-item list-group-item-action active">
                      <i class="bi bi-person me-2"></i><span>Thông tin cá nhân</span>
                    </a>
                    <a href="./profile-rewards.html" class="list-group-item list-group-item-action">
                      <i class="fas fa-gift me-2"></i><span>Đổi quà</span>
                    </a>
                    <a href="./profile-password.html" class="list-group-item list-group-item-action">
                      <i class="bi bi-key me-2"></i><span>Đổi mật khẩu</span>
                    </a>
                    <a href="./profile-settings.html" class="list-group-item list-group-item-action">
                      <i class="bi bi-gear me-2"></i><span>Cài đặt tài khoản</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 profile-content">
              <!-- Profile Info -->
              <div class="card">
                <div class="card-header">
                  <h5 class="card-title mb-0">
                    <i class="bi bi-person me-2"></i>Thông tin cá nhân
                  </h5>
                </div>
                <div class="card-body">
                    <form id="profileForm">
                      <div class="row">
                        <div class="col-md-6 mb-3">
                          <label for="profileName" class="form-label">Tên tài khoản *</label>
                          <input type="text" class="form-control" id="profileName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                          <label for="profileEmail" class="form-label">Email *</label>
                          <input type="email" class="form-control" id="profileEmail" readonly>
                          <div class="form-text">Email không thể thay đổi</div>
                        </div>
                      </div>
                      <div class="row">
                        <div class="col-md-6 mb-3">
                          <label for="profilePhone" class="form-label">Số điện thoại</label>
                          <input type="tel" class="form-control" id="profilePhone">
                        </div>
                        <div class="col-md-6 mb-3">
                          <label for="profileRole" class="form-label">Vai trò</label>
                          <input type="text" class="form-control" id="profileRole" readonly>
                        </div>
                      </div>
                      <div class="mb-3">
                        <label for="profileAddress" class="form-label">Địa chỉ</label>
                        <textarea class="form-control" id="profileAddress" rows="3"></textarea>
                      </div>
                      <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                          <i class="bi bi-check-lg me-2"></i>Cập nhật thông tin
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="resetProfileBtn">
                          <i class="bi bi-arrow-clockwise me-2"></i>Khôi phục
                        </button>
                      </div>
                      <div id="profileFormFeedback" class="mt-3"></div>
                    </form>
                  </div>
                </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- footer -->
    <include src="./layout/footer.html" locals="{}"></include>

    <!-- Bootstrap JS Bundle -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
      crossorigin="anonymous"
    ></script>

    <!-- Custom JS -->
    <script type="module" src="../script/auth.js"></script>
    <script type="module" src="../script/profile.js"></script>
  </body>
</html>

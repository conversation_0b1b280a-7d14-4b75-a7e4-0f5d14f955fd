<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="../assets/icon/recycle.svg" />
    <title>Thông tin cá nhân - Recycle Charity</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />

    <!-- Bootstrap Icons -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"
    />

    <!-- Google Fonts-->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap"
      rel="stylesheet"
    />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../stylesheets/main.scss" />

    <style>
      .profile-header-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 250px;
        display: flex;
        align-items: center;
      }

      .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      }

      .bg-gradient-warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
      }

      .bg-gradient-info {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
      }

      .btn-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        transition: all 0.3s ease;
      }

      .btn-gradient-primary:hover {
        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
      }

      .btn-gradient-warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border: none;
        color: white;
        transition: all 0.3s ease;
      }

      .btn-gradient-warning:hover {
        background: linear-gradient(135deg, #f5576c 0%, #f093fb 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4);
      }

      .card {
        border: none;
        border-radius: 15px;
        overflow: hidden;
      }

      .card-header {
        border-radius: 15px 15px 0 0 !important;
      }

      .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
      }

      .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
      }

      .list-group-item {
        border: none;
        border-radius: 10px !important;
        margin-bottom: 5px;
        transition: all 0.3s ease;
      }

      .list-group-item:hover {
        background-color: #f8f9fa;
        transform: translateX(5px);
      }

      .list-group-item.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: transparent;
      }
    </style>
  </head>
  <body class="profile-page bg-light">
    <!-- main content -->
    <main class="container-fluid p-0">
      <!-- Header Section with Gradient -->
      <div class="profile-header-section">
        <div class="container py-5">
          <div class="row justify-content-center">
            <div class="col-lg-8 text-center text-white">
              <h1 class="display-5 fw-bold mb-3">
                <i class="bi bi-person-circle me-3"></i>Thông tin cá nhân
              </h1>
              <p class="lead">Quản lý thông tin tài khoản của bạn</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="container py-5">
        <div class="row justify-content-center">
          <div class="col-lg-10">

            <!-- Profile Content -->
            <div class="row">
              <!-- Sidebar -->
              <div class="col-lg-3 mb-4 profile-sidebar">
                <div class="card shadow-sm">
                  <div class="card-body">
                    <div class="list-group list-group-flush">
                      <a href="#profile-info" class="list-group-item list-group-item-action active" data-tab="profile-info">
                        <i class="bi bi-person me-2"></i><span>Thông tin cá nhân</span>
                      </a>
                      <a href="#change-password" class="list-group-item list-group-item-action" data-tab="change-password">
                        <i class="bi bi-key me-2"></i><span>Đổi mật khẩu</span>
                      </a>
                      <a href="#account-settings" class="list-group-item list-group-item-action" data-tab="account-settings">
                        <i class="bi bi-gear me-2"></i><span>Cài đặt tài khoản</span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Main Content -->
              <div class="col-lg-9 profile-content">
                <!-- Profile Info Tab -->
                <div id="profile-info" class="tab-content-item active">
                  <div class="card shadow-sm">
                    <div class="card-header bg-gradient-primary text-white">
                      <h5 class="card-title mb-0">
                        <i class="bi bi-person me-2"></i>Thông tin cá nhân
                      </h5>
                    </div>
                    <div class="card-body">
                      <form id="profileForm">
                        <div class="row">
                          <div class="col-md-6 mb-3">
                            <label for="profileFullName" class="form-label">Họ và tên *</label>
                            <input type="text" class="form-control" id="profileFullName" required>
                          </div>
                          <div class="col-md-6 mb-3">
                            <label for="profileName" class="form-label">Tên tài khoản *</label>
                            <input type="text" class="form-control" id="profileName" required>
                          </div>
                        </div>
                        <div class="row">
                          <div class="col-md-6 mb-3">
                            <label for="profileEmail" class="form-label">Email *</label>
                            <input type="email" class="form-control" id="profileEmail" readonly>
                            <div class="form-text">Email không thể thay đổi</div>
                          </div>
                          <div class="col-md-6 mb-3">
                            <label for="profileGender" class="form-label">Giới tính</label>
                            <select class="form-select" id="profileGender">
                              <option value="">Chọn giới tính</option>
                              <option value="male">Nam</option>
                              <option value="female">Nữ</option>
                              <option value="other">Khác</option>
                            </select>
                          </div>
                        </div>
                        <div class="row">
                          <div class="col-md-6 mb-3">
                            <label for="profilePhone" class="form-label">Số điện thoại</label>
                            <input type="tel" class="form-control" id="profilePhone">
                          </div>
                          <div class="col-md-6 mb-3">
                            <label for="profileRole" class="form-label">Vai trò</label>
                            <input type="text" class="form-control" id="profileRole" readonly>
                          </div>
                        </div>
                        <div class="mb-3">
                          <label for="profileAddress" class="form-label">Địa chỉ</label>
                          <textarea class="form-control" id="profileAddress" rows="3"></textarea>
                        </div>
                        <div class="d-flex justify-content-between">
                          <button type="submit" class="btn btn-gradient-primary">
                            <i class="bi bi-check-lg me-2"></i>Cập nhật thông tin
                          </button>
                          <button type="button" class="btn btn-outline-secondary" id="resetProfileBtn">
                            <i class="bi bi-arrow-clockwise me-2"></i>Khôi phục
                          </button>
                        </div>
                        <div id="profileFormFeedback" class="mt-3"></div>
                      </form>
                    </div>
                  </div>
                </div>

                <!-- Change Password Tab -->
                <div id="change-password" class="tab-content-item">
                  <div class="card shadow-sm">
                    <div class="card-header bg-gradient-warning text-white">
                      <h5 class="card-title mb-0">
                        <i class="bi bi-key me-2"></i>Đổi mật khẩu
                      </h5>
                    </div>
                    <div class="card-body">
                      <form id="changePasswordForm">
                        <div class="mb-3">
                          <label for="currentPassword" class="form-label">Mật khẩu hiện tại *</label>
                          <input type="password" class="form-control" id="currentPassword" required>
                        </div>
                        <div class="mb-3">
                          <label for="newPassword" class="form-label">Mật khẩu mới *</label>
                          <input type="password" class="form-control" id="newPassword" required>
                          <div class="form-text">Mật khẩu phải có ít nhất 6 ký tự, bao gồm chữ hoa, chữ thường và số</div>
                        </div>
                        <div class="mb-3">
                          <label for="confirmNewPassword" class="form-label">Xác nhận mật khẩu mới *</label>
                          <input type="password" class="form-control" id="confirmNewPassword" required>
                        </div>
                        <button type="submit" class="btn btn-gradient-warning">
                          <i class="bi bi-shield-lock me-2"></i>Đổi mật khẩu
                        </button>
                        <div id="changePasswordFormFeedback" class="mt-3"></div>
                      </form>
                    </div>
                  </div>
                </div>

              <!-- Account Settings Tab -->
              <div id="account-settings" class="tab-content-item">
                <div class="card shadow-sm">
                  <div class="card-header bg-gradient-info text-white">
                    <h5 class="card-title mb-0">
                      <i class="bi bi-gear me-2"></i>Cài đặt tài khoản
                    </h5>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-6">
                        <h6>Thông tin tài khoản</h6>
                        <ul class="list-unstyled">
                          <li><strong>ID:</strong> <span id="accountId">-</span></li>
                          <li><strong>Ngày tạo:</strong> <span id="accountCreated">-</span></li>
                          <li><strong>Lần đăng nhập cuối:</strong> <span id="accountLastLogin">-</span></li>
                          <li><strong>Trạng thái:</strong> <span id="accountStatus" class="badge bg-success">Hoạt động</span></li>
                        </ul>
                      </div>
                      <div class="col-md-6">
                        <h6>Thống kê hoạt động</h6>
                        <ul class="list-unstyled">
                          <li><strong>Số lần đăng nhập:</strong> <span id="loginCount">-</span></li>
                          <li><strong>Hoạt động gần nhất:</strong> <span id="lastActivity">-</span></li>
                        </ul>
                      </div>
                    </div>
                    <hr>
                    <div class="alert alert-warning">
                      <i class="bi bi-exclamation-triangle me-2"></i>
                      <strong>Lưu ý:</strong> Nếu bạn cần hỗ trợ hoặc có vấn đề với tài khoản,
                      vui lòng liên hệ với chúng tôi qua trang <a href="./contact.html">Liên hệ</a>.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>



    <!-- Bootstrap JS Bundle -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
      crossorigin="anonymous"
    ></script>

    <!-- Custom JS -->
    <script type="module" src="../script/auth.js"></script>
    <script type="module" src="../script/profile.js"></script>
  </body>
</html>

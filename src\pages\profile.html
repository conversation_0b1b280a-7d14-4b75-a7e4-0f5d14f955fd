<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="../assets/icon/recycle.svg" />
    <title>Thông tin cá nhân - Recycle Charity</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />

    <!-- Bootstrap Icons -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"
    />

    <!-- Google Fonts-->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700;800;900&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap"
      rel="stylesheet"
    />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../stylesheets/main.scss" />
    <style>
      .reward-card {
        transition: all 0.3s ease;
        border: 1px solid #dee2e6;
        border-radius: 12px;
        overflow: hidden;
      }

      .reward-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border-color: #0d6efd;
      }

      .reward-card .card-img-top {
        transition: transform 0.3s ease;
      }

      .reward-card:hover .card-img-top {
        transform: scale(1.05);
      }

      .btn-group .btn {
        margin: 2px;
        border-radius: 20px;
      }

      .points-badge {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 25px;
        border-radius: 15px;
        display: inline-flex;
        align-items: center;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
      }

      .fade-in {
        animation: fadeIn 0.5s ease-in-out;
      }

      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
      }
    </style>
  </head>
  <body class="profile-page">
    <!-- header -->
    <include src="./layout/header.html" locals="{}"></include>

    <!-- main content -->
    <main class="container my-5">
      <div class="row justify-content-center">
        <div class="col-lg-10">
          <!-- Page Header -->
          <div class="profile-header text-center">
            <h1 class="display-5 fw-bold">
              <i class="bi bi-person-circle me-3"></i>Thông tin cá nhân
            </h1>
            <p class="lead">Quản lý thông tin tài khoản của bạn</p>
          </div>

          <!-- Profile Content -->
          <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 mb-4 profile-sidebar">
              <div class="card">
                <div class="card-body">
                  <div class="list-group list-group-flush">
                    <a href="#profile-info" class="list-group-item list-group-item-action active" data-tab="profile-info">
                      <i class="bi bi-person me-2"></i><span>Thông tin cá nhân</span>
                    </a>
                    <a href="#rewards" class="list-group-item list-group-item-action" data-tab="rewards">
                      <i class="fas fa-gift me-2"></i><span>Đổi quà</span>
                    </a>
                    <a href="#change-password" class="list-group-item list-group-item-action" data-tab="change-password">
                      <i class="bi bi-key me-2"></i><span>Đổi mật khẩu</span>
                    </a>
                    <a href="#account-settings" class="list-group-item list-group-item-action" data-tab="account-settings">
                      <i class="bi bi-gear me-2"></i><span>Cài đặt tài khoản</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 profile-content">
              <!-- Profile Info Tab -->
              <div id="profile-info" class="tab-content-item active">
                <div class="card">
                  <div class="card-header">
                    <h5 class="card-title mb-0">
                      <i class="bi bi-person me-2"></i>Thông tin cá nhân
                    </h5>
                  </div>
                  <div class="card-body">
                    <form id="profileForm">
                      <div class="row">
                        <div class="col-md-6 mb-3">
                          <label for="profileName" class="form-label">Tên tài khoản *</label>
                          <input type="text" class="form-control" id="profileName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                          <label for="profileEmail" class="form-label">Email *</label>
                          <input type="email" class="form-control" id="profileEmail" readonly>
                          <div class="form-text">Email không thể thay đổi</div>
                        </div>
                      </div>
                      <div class="row">
                        <div class="col-md-6 mb-3">
                          <label for="profilePhone" class="form-label">Số điện thoại</label>
                          <input type="tel" class="form-control" id="profilePhone">
                        </div>
                        <div class="col-md-6 mb-3">
                          <label for="profileRole" class="form-label">Vai trò</label>
                          <input type="text" class="form-control" id="profileRole" readonly>
                        </div>
                      </div>
                      <div class="mb-3">
                        <label for="profileAddress" class="form-label">Địa chỉ</label>
                        <textarea class="form-control" id="profileAddress" rows="3"></textarea>
                      </div>
                      <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                          <i class="bi bi-check-lg me-2"></i>Cập nhật thông tin
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="resetProfileBtn">
                          <i class="bi bi-arrow-clockwise me-2"></i>Khôi phục
                        </button>
                      </div>
                      <div id="profileFormFeedback" class="mt-3"></div>
                    </form>
                  </div>
                </div>
              </div>

              <!-- Rewards Tab -->
              <div id="rewards" class="tab-content-item">
                <div class="card">
                  <div class="card-header">
                    <h5 class="card-title mb-0">
                      <i class="fas fa-gift me-2"></i>Đổi quà tích xu
                    </h5>
                  </div>
                  <div class="card-body">
                    <!-- User Points Display -->
                    <div class="row mb-4">
                      <div class="col-12">
                        <div class="alert alert-info d-flex align-items-center">
                          <i class="fas fa-wallet me-3 fs-4"></i>
                          <div>
                            <h6 class="mb-1">Xu của bạn</h6>
                            <span class="fs-4 fw-bold text-primary" id="userPoints">1,250</span>
                            <span class="text-muted">xu</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Category Filters -->
                    <div class="row mb-4">
                      <div class="col-12">
                        <h6 class="mb-3">
                          <i class="fas fa-tags me-2"></i>Danh mục quà tặng
                        </h6>
                        <div class="btn-group flex-wrap" role="group">
                          <button type="button" class="btn btn-outline-primary active" data-category="all">
                            <i class="fas fa-th-large me-1"></i>Tất cả
                          </button>
                          <button type="button" class="btn btn-outline-primary" data-category="handmade">
                            <i class="fas fa-hand-holding-heart me-1"></i>Đồ thủ công
                          </button>
                          <button type="button" class="btn btn-outline-primary" data-category="toys">
                            <i class="fas fa-teddy-bear me-1"></i>Đồ chơi
                          </button>
                          <button type="button" class="btn btn-outline-primary" data-category="accessories">
                            <i class="fas fa-key me-1"></i>Phụ kiện
                          </button>
                        </div>
                      </div>
                    </div>

                    <!-- Rewards Grid -->
                    <div class="row g-3" id="rewardsGrid">
                      <!-- Reward Item 1 -->
                      <div class="col-lg-4 col-md-6">
                        <div class="card h-100 reward-card">
                          <div class="position-relative">
                            <img src="../assets/images/rewards/teddy-bear.webp" class="card-img-top" alt="Gấu bông handmade" style="height: 200px; object-fit: cover;">
                            <span class="badge bg-success position-absolute top-0 end-0 m-2">Handmade</span>
                            <div class="position-absolute bottom-0 start-0 m-2">
                              <span class="badge bg-warning text-dark">
                                <i class="fas fa-coins me-1"></i>500 xu
                              </span>
                            </div>
                          </div>
                          <div class="card-body">
                            <h6 class="card-title">Gấu bông handmade</h6>
                            <p class="card-text small text-muted">Gấu bông được các em nhỏ làm thủ công từ vải tái chế.</p>
                            <div class="d-flex justify-content-between align-items-center">
                              <small class="text-success">Còn 15 món</small>
                              <button class="btn btn-primary btn-sm">
                                <i class="fas fa-exchange-alt me-1"></i>Đổi
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Reward Item 2 -->
                      <div class="col-lg-4 col-md-6">
                        <div class="card h-100 reward-card">
                          <div class="position-relative">
                            <img src="../assets/images/rewards/keychain.webp" class="card-img-top" alt="Móc khóa tái chế" style="height: 200px; object-fit: cover;">
                            <span class="badge bg-info position-absolute top-0 end-0 m-2">Eco-friendly</span>
                            <div class="position-absolute bottom-0 start-0 m-2">
                              <span class="badge bg-warning text-dark">
                                <i class="fas fa-coins me-1"></i>200 xu
                              </span>
                            </div>
                          </div>
                          <div class="card-body">
                            <h6 class="card-title">Móc khóa tái chế</h6>
                            <p class="card-text small text-muted">Móc khóa được làm từ nhựa tái chế, thiết kế sáng tạo.</p>
                            <div class="d-flex justify-content-between align-items-center">
                              <small class="text-success">Còn 25 món</small>
                              <button class="btn btn-primary btn-sm">
                                <i class="fas fa-exchange-alt me-1"></i>Đổi
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Reward Item 3 -->
                      <div class="col-lg-4 col-md-6">
                        <div class="card h-100 reward-card">
                          <div class="position-relative">
                            <img src="../assets/images/rewards/painting.webp" class="card-img-top" alt="Tranh vẽ tay" style="height: 200px; object-fit: cover;">
                            <span class="badge bg-danger position-absolute top-0 end-0 m-2">Limited</span>
                            <div class="position-absolute bottom-0 start-0 m-2">
                              <span class="badge bg-warning text-dark">
                                <i class="fas fa-coins me-1"></i>800 xu
                              </span>
                            </div>
                          </div>
                          <div class="card-body">
                            <h6 class="card-title">Tranh vẽ tay</h6>
                            <p class="card-text small text-muted">Bức tranh được vẽ bởi các em nhỏ, thể hiện tình yêu môi trường.</p>
                            <div class="d-flex justify-content-between align-items-center">
                              <small class="text-warning">Còn 8 món</small>
                              <button class="btn btn-primary btn-sm">
                                <i class="fas fa-exchange-alt me-1"></i>Đổi
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Reward Item 4 -->
                      <div class="col-lg-4 col-md-6">
                        <div class="card h-100 reward-card">
                          <div class="position-relative">
                            <img src="../assets/images/rewards/canvas-bag.webp" class="card-img-top" alt="Túi vải canvas" style="height: 200px; object-fit: cover;">
                            <span class="badge bg-info position-absolute top-0 end-0 m-2">Eco-friendly</span>
                            <div class="position-absolute bottom-0 start-0 m-2">
                              <span class="badge bg-warning text-dark">
                                <i class="fas fa-coins me-1"></i>350 xu
                              </span>
                            </div>
                          </div>
                          <div class="card-body">
                            <h6 class="card-title">Túi vải canvas</h6>
                            <p class="card-text small text-muted">Túi vải được thiết kế và in hình bởi các em.</p>
                            <div class="d-flex justify-content-between align-items-center">
                              <small class="text-success">Còn 20 món</small>
                              <button class="btn btn-primary btn-sm">
                                <i class="fas fa-exchange-alt me-1"></i>Đổi
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Reward Item 5 -->
                      <div class="col-lg-4 col-md-6">
                        <div class="card h-100 reward-card">
                          <div class="position-relative">
                            <img src="../assets/images/rewards/wool-doll.webp" class="card-img-top" alt="Búp bê len" style="height: 200px; object-fit: cover;">
                            <span class="badge bg-success position-absolute top-0 end-0 m-2">Handmade</span>
                            <div class="position-absolute bottom-0 start-0 m-2">
                              <span class="badge bg-warning text-dark">
                                <i class="fas fa-coins me-1"></i>600 xu
                              </span>
                            </div>
                          </div>
                          <div class="card-body">
                            <h6 class="card-title">Búp bê len</h6>
                            <p class="card-text small text-muted">Búp bê được đan len thủ công bởi các em nhỏ.</p>
                            <div class="d-flex justify-content-between align-items-center">
                              <small class="text-success">Còn 12 món</small>
                              <button class="btn btn-primary btn-sm">
                                <i class="fas fa-exchange-alt me-1"></i>Đổi
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Reward Item 6 -->
                      <div class="col-lg-4 col-md-6">
                        <div class="card h-100 reward-card">
                          <div class="position-relative">
                            <img src="../assets/images/rewards/bookmark.webp" class="card-img-top" alt="Bookmark handmade" style="height: 200px; object-fit: cover;">
                            <span class="badge bg-info position-absolute top-0 end-0 m-2">Eco-friendly</span>
                            <div class="position-absolute bottom-0 start-0 m-2">
                              <span class="badge bg-warning text-dark">
                                <i class="fas fa-coins me-1"></i>150 xu
                              </span>
                            </div>
                          </div>
                          <div class="card-body">
                            <h6 class="card-title">Bookmark handmade</h6>
                            <p class="card-text small text-muted">Bookmark được làm từ giấy tái chế, trang trí đẹp mắt.</p>
                            <div class="d-flex justify-content-between align-items-center">
                              <small class="text-success">Còn 30 món</small>
                              <button class="btn btn-primary btn-sm">
                                <i class="fas fa-exchange-alt me-1"></i>Đổi
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Reward Item 7 -->
                      <div class="col-lg-4 col-md-6">
                        <div class="card h-100 reward-card">
                          <div class="position-relative">
                            <img src="../assets/images/rewards/pencil-box.webp" class="card-img-top" alt="Hộp bút handmade" style="height: 200px; object-fit: cover;">
                            <span class="badge bg-success position-absolute top-0 end-0 m-2">Handmade</span>
                            <div class="position-absolute bottom-0 start-0 m-2">
                              <span class="badge bg-warning text-dark">
                                <i class="fas fa-coins me-1"></i>400 xu
                              </span>
                            </div>
                          </div>
                          <div class="card-body">
                            <h6 class="card-title">Hộp bút handmade</h6>
                            <p class="card-text small text-muted">Hộp bút được làm từ carton tái chế, trang trí sáng tạo.</p>
                            <div class="d-flex justify-content-between align-items-center">
                              <small class="text-success">Còn 18 món</small>
                              <button class="btn btn-primary btn-sm">
                                <i class="fas fa-exchange-alt me-1"></i>Đổi
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Reward Item 8 -->
                      <div class="col-lg-4 col-md-6">
                        <div class="card h-100 reward-card">
                          <div class="position-relative">
                            <img src="../assets/images/rewards/wooden-toy-car.webp" class="card-img-top" alt="Xe đồ chơi gỗ" style="height: 200px; object-fit: cover;">
                            <span class="badge bg-info position-absolute top-0 end-0 m-2">Eco-friendly</span>
                            <div class="position-absolute bottom-0 start-0 m-2">
                              <span class="badge bg-warning text-dark">
                                <i class="fas fa-coins me-1"></i>700 xu
                              </span>
                            </div>
                          </div>
                          <div class="card-body">
                            <h6 class="card-title">Xe đồ chơi gỗ</h6>
                            <p class="card-text small text-muted">Xe đồ chơi được làm từ gỗ tái chế, an toàn và thân thiện.</p>
                            <div class="d-flex justify-content-between align-items-center">
                              <small class="text-success">Còn 10 món</small>
                              <button class="btn btn-primary btn-sm">
                                <i class="fas fa-exchange-alt me-1"></i>Đổi
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Change Password Tab -->
              <div id="change-password" class="tab-content-item">
                <div class="card">
                  <div class="card-header">
                    <h5 class="card-title mb-0">
                      <i class="bi bi-key me-2"></i>Đổi mật khẩu
                    </h5>
                  </div>
                  <div class="card-body">
                    <form id="changePasswordForm">
                      <div class="mb-3">
                        <label for="currentPassword" class="form-label">Mật khẩu hiện tại *</label>
                        <input type="password" class="form-control" id="currentPassword" required>
                      </div>
                      <div class="mb-3">
                        <label for="newPassword" class="form-label">Mật khẩu mới *</label>
                        <input type="password" class="form-control" id="newPassword" required>
                        <div class="form-text">Mật khẩu phải có ít nhất 6 ký tự, bao gồm chữ hoa, chữ thường và số</div>
                      </div>
                      <div class="mb-3">
                        <label for="confirmNewPassword" class="form-label">Xác nhận mật khẩu mới *</label>
                        <input type="password" class="form-control" id="confirmNewPassword" required>
                      </div>
                      <button type="submit" class="btn btn-warning">
                        <i class="bi bi-shield-lock me-2"></i>Đổi mật khẩu
                      </button>
                      <div id="changePasswordFormFeedback" class="mt-3"></div>
                    </form>
                  </div>
                </div>
              </div>

              <!-- Account Settings Tab -->
              <div id="account-settings" class="tab-content-item">
                <div class="card">
                  <div class="card-header">
                    <h5 class="card-title mb-0">
                      <i class="bi bi-gear me-2"></i>Cài đặt tài khoản
                    </h5>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-6">
                        <h6>Thông tin tài khoản</h6>
                        <ul class="list-unstyled">
                          <li><strong>ID:</strong> <span id="accountId">-</span></li>
                          <li><strong>Ngày tạo:</strong> <span id="accountCreated">-</span></li>
                          <li><strong>Lần đăng nhập cuối:</strong> <span id="accountLastLogin">-</span></li>
                          <li><strong>Trạng thái:</strong> <span id="accountStatus" class="badge bg-success">Hoạt động</span></li>
                        </ul>
                      </div>
                      <div class="col-md-6">
                        <h6>Thống kê hoạt động</h6>
                        <ul class="list-unstyled">
                          <li><strong>Số lần đăng nhập:</strong> <span id="loginCount">-</span></li>
                          <li><strong>Hoạt động gần nhất:</strong> <span id="lastActivity">-</span></li>
                        </ul>
                      </div>
                    </div>
                    <hr>
                    <div class="alert alert-warning">
                      <i class="bi bi-exclamation-triangle me-2"></i>
                      <strong>Lưu ý:</strong> Nếu bạn cần hỗ trợ hoặc có vấn đề với tài khoản,
                      vui lòng liên hệ với chúng tôi qua trang <a href="./contact.html">Liên hệ</a>.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- footer -->
    <include src="./layout/footer.html" locals="{}"></include>

    <!-- Bootstrap JS Bundle -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
      crossorigin="anonymous"
    ></script>

    <!-- Custom JS -->
    <script type="module" src="../script/auth.js"></script>
    <script type="module" src="../script/profile.js"></script>
    <script type="module" src="../script/rewards.js"></script>
  </body>
</html>
